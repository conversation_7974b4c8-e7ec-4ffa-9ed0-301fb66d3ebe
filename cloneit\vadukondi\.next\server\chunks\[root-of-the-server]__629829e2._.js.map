{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production'\nconst SALT_ROUNDS = 12\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, SALT_ROUNDS)\n}\n\nexport async function verifyPassword(password: string, hash: string): Promise<boolean> {\n  return bcrypt.compare(password, hash)\n}\n\nexport function generateToken(userId: string): string {\n  return jwt.sign(\n    { userId, iat: Date.now() },\n    JWT_SECRET,\n    { expiresIn: '7d' }\n  )\n}\n\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string }\n    return decoded\n  } catch (error) {\n    return null\n  }\n}\n\nexport function extractTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7)\n  }\n  \n  // Also check cookies\n  const token = request.cookies.get('auth-token')?.value\n  return token || null\n}\n\nexport function getUserFromRequest(request: NextRequest): { userId: string } | null {\n  const token = extractTokenFromRequest(request)\n  if (!token) return null\n  \n  return verifyToken(token)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,cAAc;AAEb,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,IAAY;IACjE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CACb;QAAE;QAAQ,KAAK,KAAK,GAAG;IAAG,GAC1B,YACA;QAAE,WAAW;IAAK;AAEtB;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,wBAAwB,OAAoB;IAC1D,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,qBAAqB;IACrB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,QAAQ,wBAAwB;IACtC,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Generate a random 6-digit ID\nexport function generateUserId(): string {\n  return Math.floor(Math.random() * 1000000).toString().padStart(6, '0')\n}\n\n// Validate 6-digit ID format\nexport function isValidUserId(id: string): boolean {\n  return /^\\d{6}$/.test(id)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Generate unique file ID\nexport function generateFileId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2)\n}\n\n// Format date string\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString()\n}\n\n// Get file category from MIME type\nexport function getFileCategory(mimeType: string): string {\n  if (mimeType.startsWith('image/')) return 'image'\n  if (mimeType.startsWith('video/')) return 'video'\n  if (mimeType.startsWith('audio/')) return 'audio'\n  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) return 'archive'\n  if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) return 'document'\n  return 'other'\n}\n\n// Validate user ID (alias for backward compatibility)\nexport function validateUserId(userId: string): boolean {\n  return isValidUserId(userId)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG;AACpE;AAGO,SAAS,cAAc,EAAU;IACtC,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS;IACd,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB;AAChD;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,SAAS,QAAQ,CAAC,UAAU,SAAS,QAAQ,CAAC,UAAU,SAAS,QAAQ,CAAC,QAAQ,OAAO;IAC7F,IAAI,SAAS,QAAQ,CAAC,UAAU,SAAS,QAAQ,CAAC,eAAe,SAAS,QAAQ,CAAC,SAAS,OAAO;IACnG,OAAO;AACT;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,cAAc;AACvB", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/app/api/files/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { verifyToken } from '@/lib/auth'\nimport { generateFileId } from '@/lib/utils'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Verify authentication\n    const token = request.headers.get('authorization')?.replace('Bearer ', '')\n    if (!token) {\n      return NextResponse.json(\n        { success: false, error: 'Authentication required' },\n        { status: 401 }\n      )\n    }\n\n    const decoded = verifyToken(token)\n    if (!decoded) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid token' },\n        { status: 401 }\n      )\n    }\n\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n    const isPublic = formData.get('isPublic') === 'true'\n\n    if (!file) {\n      return NextResponse.json(\n        { success: false, error: 'No file provided' },\n        { status: 400 }\n      )\n    }\n\n    // Generate unique file ID\n    const fileId = generateFileId()\n    \n    // In a real implementation, you would:\n    // 1. Save the file to disk or cloud storage\n    // 2. Store file metadata in database\n    // 3. Handle file validation and virus scanning\n    \n    // For demo purposes, we'll just simulate the upload\n    const fileMetadata = {\n      id: fileId,\n      name: file.name,\n      size: file.size,\n      type: file.type,\n      uploaderId: decoded.userId,\n      isPublic,\n      uploadedAt: new Date().toISOString(),\n      downloads: 0\n    }\n\n    // Simulate processing time\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    return NextResponse.json({\n      success: true,\n      message: 'File uploaded successfully',\n      file: fileMetadata\n    })\n\n  } catch (error) {\n    console.error('Upload error:', error)\n    return NextResponse.json(\n      { success: false, error: 'Upload failed' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,wBAAwB;QACxB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QACvE,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA0B,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAgB,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,WAAW,SAAS,GAAG,CAAC,gBAAgB;QAE9C,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAmB,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,MAAM,SAAS,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAE5B,uCAAuC;QACvC,4CAA4C;QAC5C,qCAAqC;QACrC,+CAA+C;QAE/C,oDAAoD;QACpD,MAAM,eAAe;YACnB,IAAI;YACJ,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,YAAY,QAAQ,MAAM;YAC1B;YACA,YAAY,IAAI,OAAO,WAAW;YAClC,WAAW;QACb;QAEA,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAgB,GACzC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}