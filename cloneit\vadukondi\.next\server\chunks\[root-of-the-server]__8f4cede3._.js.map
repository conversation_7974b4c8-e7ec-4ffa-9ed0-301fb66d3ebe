{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production'\nconst SALT_ROUNDS = 12\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, SALT_ROUNDS)\n}\n\nexport async function verifyPassword(password: string, hash: string): Promise<boolean> {\n  return bcrypt.compare(password, hash)\n}\n\nexport function generateToken(userId: string): string {\n  return jwt.sign(\n    { userId, iat: Date.now() },\n    JWT_SECRET,\n    { expiresIn: '7d' }\n  )\n}\n\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string }\n    return decoded\n  } catch (error) {\n    return null\n  }\n}\n\nexport function extractTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7)\n  }\n  \n  // Also check cookies\n  const token = request.cookies.get('auth-token')?.value\n  return token || null\n}\n\nexport function getUserFromRequest(request: NextRequest): { userId: string } | null {\n  const token = extractTokenFromRequest(request)\n  if (!token) return null\n  \n  return verifyToken(token)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,cAAc;AAEb,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,IAAY;IACjE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CACb;QAAE;QAAQ,KAAK,KAAK,GAAG;IAAG,GAC1B,YACA;QAAE,WAAW;IAAK;AAEtB;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,wBAAwB,OAAoB;IAC1D,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,qBAAqB;IACrB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,QAAQ,wBAAwB;IACtC,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/app/api/files/download/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { verifyToken } from '@/lib/auth'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    // Verify authentication\n    const token = request.headers.get('authorization')?.replace('Bearer ', '')\n    if (!token) {\n      return NextResponse.json(\n        { success: false, error: 'Authentication required' },\n        { status: 401 }\n      )\n    }\n\n    const decoded = verifyToken(token)\n    if (!decoded) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid token' },\n        { status: 401 }\n      )\n    }\n\n    const fileId = params.id\n\n    // In a real implementation, you would:\n    // 1. Look up file metadata in database\n    // 2. Check if user has permission to download\n    // 3. Stream the actual file from storage\n    // 4. Update download count\n\n    // For demo purposes, create a sample PDF\n    const samplePdfContent = `%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n/Resources <<\n/Font <<\n/F1 5 0 R\n>>\n>>\n>>\nendobj\n\n4 0 obj\n<<\n/Length 200\n>>\nstream\nBT\n/F1 24 Tf\n100 700 Td\n(Vadukondi Sample File) Tj\n0 -50 Td\n/F1 12 Tf\n(Created by: Shaik Mahammad Yaseen) Tj\n0 -30 Td\n(This is a sample PDF file from Vadukondi) Tj\n0 -20 Td\n(Privacy-focused P2P file sharing platform) Tj\n0 -30 Td\n(File ID: ${fileId}) Tj\n0 -20 Td\n(Downloaded on: ${new Date().toLocaleString()}) Tj\nET\nendstream\nendobj\n\n5 0 obj\n<<\n/Type /Font\n/Subtype /Type1\n/BaseFont /Helvetica\n>>\nendobj\n\nxref\n0 6\n0000000000 65535 f \n0000000010 00000 n \n0000000053 00000 n \n0000000125 00000 n \n0000000348 00000 n \n0000000565 00000 n \ntrailer\n<<\n/Size 6\n/Root 1 0 R\n>>\nstartxref\n625\n%%EOF`\n\n    const buffer = Buffer.from(samplePdfContent, 'utf-8')\n\n    return new NextResponse(buffer, {\n      headers: {\n        'Content-Type': 'application/pdf',\n        'Content-Disposition': `attachment; filename=\"vadukondi-sample-${fileId}.pdf\"`,\n        'Content-Length': buffer.length.toString(),\n      },\n    })\n\n  } catch (error) {\n    console.error('Download error:', error)\n    return NextResponse.json(\n      { success: false, error: 'Download failed' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,wBAAwB;QACxB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QACvE,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA0B,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAgB,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,OAAO,EAAE;QAExB,uCAAuC;QACvC,uCAAuC;QACvC,8CAA8C;QAC9C,yCAAyC;QACzC,2BAA2B;QAE3B,yCAAyC;QACzC,MAAM,mBAAmB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UA+CpB,EAAE,OAAO;;gBAEH,EAAE,IAAI,OAAO,cAAc,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BzC,CAAC;QAEF,MAAM,SAAS,OAAO,IAAI,CAAC,kBAAkB;QAE7C,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,QAAQ;YAC9B,SAAS;gBACP,gBAAgB;gBAChB,uBAAuB,CAAC,uCAAuC,EAAE,OAAO,KAAK,CAAC;gBAC9E,kBAAkB,OAAO,MAAM,CAAC,QAAQ;YAC1C;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAkB,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}