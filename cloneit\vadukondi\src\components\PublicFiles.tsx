'use client'

import { useState, useEffect } from 'react'
import { X, Download, Search, Filter, File, Image, Video, Music, Archive } from 'lucide-react'

interface PublicFilesProps {
  onClose: () => void
}

interface PublicFile {
  id: string
  name: string
  size: number
  type: string
  uploaderId: string
  uploadedAt: string
  downloads: number
  category: string
}

export default function PublicFiles({ onClose }: PublicFilesProps) {
  const [files, setFiles] = useState<PublicFile[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setFiles([
        {
          id: '1',
          name: 'Sample_Document.pdf',
          size: 2048576,
          type: 'application/pdf',
          uploaderId: '123456',
          uploadedAt: '2024-01-15T10:30:00Z',
          downloads: 45,
          category: 'document'
        },
        {
          id: '2',
          name: 'Nature_Photo.jpg',
          size: 5242880,
          type: 'image/jpeg',
          uploaderId: '789012',
          uploadedAt: '2024-01-14T15:20:00Z',
          downloads: 128,
          category: 'image'
        },
        {
          id: '3',
          name: 'Music_Track.mp3',
          size: 8388608,
          type: 'audio/mpeg',
          uploaderId: '345678',
          uploadedAt: '2024-01-13T09:45:00Z',
          downloads: 67,
          category: 'audio'
        },
        {
          id: '4',
          name: 'Project_Archive.zip',
          size: 15728640,
          type: 'application/zip',
          uploaderId: '901234',
          uploadedAt: '2024-01-12T14:10:00Z',
          downloads: 23,
          category: 'archive'
        },
        {
          id: '5',
          name: 'Tutorial_Video.mp4',
          size: 52428800,
          type: 'video/mp4',
          uploaderId: '567890',
          uploadedAt: '2024-01-11T11:30:00Z',
          downloads: 89,
          category: 'video'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getFileIcon = (type: string, category: string) => {
    if (category === 'image') return <Image className="h-6 w-6 text-green-600" />
    if (category === 'video') return <Video className="h-6 w-6 text-red-600" />
    if (category === 'audio') return <Music className="h-6 w-6 text-purple-600" />
    if (category === 'archive') return <Archive className="h-6 w-6 text-orange-600" />
    return <File className="h-6 w-6 text-blue-600" />
  }

  const handleDownload = async (file: PublicFile) => {
    try {
      // Simulate download
      const response = await fetch(`/api/files/download/${file.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`
        }
      })
      
      if (response.ok) {
        // Create download link
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = file.name
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        // Update download count
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, downloads: f.downloads + 1 } : f
        ))
      }
    } catch (error) {
      console.error('Download failed:', error)
      alert('Download failed. Please try again.')
    }
  }

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || file.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Public Files</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b bg-gray-50 dark:bg-gray-700">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search files..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
              >
                <option value="all">All Categories</option>
                <option value="document">Documents</option>
                <option value="image">Images</option>
                <option value="video">Videos</option>
                <option value="audio">Audio</option>
                <option value="archive">Archives</option>
              </select>
            </div>
          </div>
        </div>

        {/* File List */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600 dark:text-gray-400">Loading public files...</p>
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="text-center py-8">
              <File className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">No files found</p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredFiles.map((file) => (
                <div key={file.id} className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                  {getFileIcon(file.type, file.category)}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {file.name}
                    </h3>
                    <div className="flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-400">
                      <span>{formatFileSize(file.size)}</span>
                      <span>By: {file.uploaderId}</span>
                      <span>{formatDate(file.uploadedAt)}</span>
                      <span>{file.downloads} downloads</span>
                    </div>
                  </div>
                  <button
                    onClick={() => handleDownload(file)}
                    className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <Download className="h-4 w-4" />
                    <span>Download</span>
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
