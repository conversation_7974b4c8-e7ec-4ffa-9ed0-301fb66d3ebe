<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vadukondi - Complete Source Code Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 30px;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #1e40af;
            font-size: 3em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .header .subtitle {
            color: #6b7280;
            font-size: 1.2em;
            margin: 10px 0;
        }
        .creator {
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 30px 0;
        }
        .creator h2 {
            margin: 0;
            font-size: 1.8em;
        }
        .creator p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .section {
            margin: 40px 0;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #1e40af;
            border-left: 5px solid #2563eb;
            padding-left: 15px;
            font-size: 1.8em;
        }
        .section h3 {
            color: #374151;
            margin-top: 30px;
            font-size: 1.4em;
        }
        .code-block {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }
        .file-header {
            background: #2563eb;
            color: white;
            padding: 10px 20px;
            margin: 30px 0 0 0;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
        }
        .file-content {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-top: none;
            border-radius: 0 0 8px 8px;
            padding: 20px;
            margin: 0 0 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            line-height: 1.4;
            white-space: pre-wrap;
        }
        .toc {
            background: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
        }
        .toc h3 {
            margin-top: 0;
            color: #1e40af;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        .toc li:before {
            content: "→";
            position: absolute;
            left: 0;
            color: #2563eb;
            font-weight: bold;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-item {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }
        .feature-item h4 {
            margin: 0 0 10px 0;
            color: #1e40af;
        }
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        .tech-item {
            background: #2563eb;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Vadukondi</h1>
            <div class="subtitle">Privacy-Focused P2P File Sharing Platform</div>
            <div class="subtitle">Complete Source Code Documentation</div>
        </div>

        <div class="creator">
            <h2>👨‍💻 Created by Shaik Mahammad Yaseen</h2>
            <p>Full-Stack Developer & Privacy Advocate</p>
            <p>Built with modern web technologies and Swiss-style precision</p>
        </div>

        <div class="toc">
            <h3>📋 Table of Contents</h3>
            <ul>
                <li>Project Overview & Features</li>
                <li>Technology Stack</li>
                <li>Project Structure</li>
                <li>Frontend Components</li>
                <li>Backend API Routes</li>
                <li>Database Models</li>
                <li>Utility Functions</li>
                <li>Configuration Files</li>
                <li>Installation & Setup</li>
                <li>API Documentation</li>
            </ul>
        </div>

        <div class="section">
            <h2>🚀 Project Overview</h2>
            <p><strong>Vadukondi</strong> is a cutting-edge, privacy-focused peer-to-peer file sharing platform that embodies Swiss-style precision, reliability, and security. The platform enables anonymous file sharing using 6-digit numerical IDs as the sole user identifier, requiring no email verification or personal information.</p>
            
            <div class="feature-list">
                <div class="feature-item">
                    <h4>🔐 Anonymous Authentication</h4>
                    <p>6-digit ID system (000000-999999) with no personal data collection</p>
                </div>
                <div class="feature-item">
                    <h4>🌐 Hybrid Sharing</h4>
                    <p>Cloud-based global sharing + WebRTC P2P for local networks</p>
                </div>
                <div class="feature-item">
                    <h4>🔒 End-to-End Encryption</h4>
                    <p>Secure file transfers with client-side encryption</p>
                </div>
                <div class="feature-item">
                    <h4>🤖 AI Integration</h4>
                    <p>OpenRouter API for file categorization and content analysis</p>
                </div>
                <div class="feature-item">
                    <h4>📱 Responsive Design</h4>
                    <p>Modern UI with dark mode and mobile optimization</p>
                </div>
                <div class="feature-item">
                    <h4>⚡ Real-time Features</h4>
                    <p>Socket.io for live notifications and peer discovery</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🛠️ Technology Stack</h2>
            <div class="tech-stack">
                <span class="tech-item">Next.js 15.3.4</span>
                <span class="tech-item">React 19</span>
                <span class="tech-item">TypeScript</span>
                <span class="tech-item">Tailwind CSS</span>
                <span class="tech-item">MongoDB</span>
                <span class="tech-item">Mongoose</span>
                <span class="tech-item">JWT Authentication</span>
                <span class="tech-item">bcrypt</span>
                <span class="tech-item">WebRTC</span>
                <span class="tech-item">Socket.io</span>
                <span class="tech-item">Radix UI</span>
                <span class="tech-item">Lucide Icons</span>
                <span class="tech-item">OpenRouter API</span>
            </div>
        </div>

        <div class="section">
            <h2>📁 Project Structure</h2>
            <div class="code-block">
vadukondi/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API Routes
│   │   │   ├── auth/          # Authentication endpoints
│   │   │   └── files/         # File management endpoints
│   │   ├── dashboard/         # Dashboard page
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Homepage
│   ├── components/            # React components
│   │   ├── AuthForm.tsx       # Authentication form
│   │   ├── FileUpload.tsx     # File upload component
│   │   ├── NetworkScan.tsx    # Network scanning
│   │   └── PublicFiles.tsx    # Public file browser
│   ├── lib/                   # Utility libraries
│   │   ├── auth.ts            # JWT utilities
│   │   ├── database.ts        # MongoDB connection
│   │   ├── memory-store.ts    # Fallback storage
│   │   └── utils.ts           # Helper functions
│   ├── models/                # Database models
│   │   └── User.ts            # User schema
│   └── types/                 # TypeScript definitions
├── public/                    # Static assets
├── package.json               # Dependencies
├── tailwind.config.ts         # Tailwind configuration
├── tsconfig.json              # TypeScript configuration
└── next.config.ts             # Next.js configuration
            </div>
        </div>

        <div class="section">
            <h2>🏠 Frontend Components</h2>

            <div class="file-header">📄 src/app/page.tsx - Homepage Component</div>
            <div class="file-content">'use client'

import { useEffect, useState } from 'react'
import { Share2, Shield, Users, Zap, Globe, Lock } from 'lucide-react'
import AuthForm from '@/components/AuthForm'

export default function Home() {
  const [showAuth, setShowAuth] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    &lt;div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800"&gt;
      &lt;header className="container mx-auto px-4 py-6"&gt;
        &lt;nav className="flex items-center justify-between"&gt;
          &lt;div className="flex items-center space-x-2"&gt;
            &lt;Share2 className="h-8 w-8 text-blue-600" /&gt;
            &lt;span className="text-2xl font-bold text-gray-900 dark:text-white"&gt;Vadukondi&lt;/span&gt;
          &lt;/div&gt;
          &lt;button
            onClick={() =&gt; setShowAuth(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          &gt;
            Get Started
          &lt;/button&gt;
        &lt;/nav&gt;
      &lt;/header&gt;

      &lt;main className="container mx-auto px-4 py-12"&gt;
        &lt;div className="text-center mb-16"&gt;
          &lt;h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6"&gt;
            Privacy-First File Sharing
          &lt;/h1&gt;
          &lt;p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto"&gt;
            Share files securely with anonymous 6-digit IDs. No email required, no personal data collected.
            Experience Swiss-style precision in peer-to-peer file sharing.
          &lt;/p&gt;
          &lt;div className="flex flex-col sm:flex-row gap-4 justify-center"&gt;
            &lt;button
              onClick={() =&gt; setShowAuth(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors"
            &gt;
              Start Sharing
            &lt;/button&gt;
            &lt;button className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 px-8 py-3 rounded-lg text-lg font-semibold transition-colors"&gt;
              Learn More
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;

        &lt;div className="grid md:grid-cols-3 gap-8 mb-16"&gt;
          &lt;div className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg"&gt;
            &lt;Shield className="h-12 w-12 text-blue-600 mb-4" /&gt;
            &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3"&gt;Anonymous &amp; Secure&lt;/h3&gt;
            &lt;p className="text-gray-600 dark:text-gray-300"&gt;
              Use only 6-digit IDs. No email, no personal information. Your privacy is our priority.
            &lt;/p&gt;
          &lt;/div&gt;
          &lt;div className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg"&gt;
            &lt;Users className="h-12 w-12 text-green-600 mb-4" /&gt;
            &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3"&gt;P2P Network&lt;/h3&gt;
            &lt;p className="text-gray-600 dark:text-gray-300"&gt;
              Direct device-to-device transfers using WebRTC. Fast, efficient, and decentralized.
            &lt;/p&gt;
          &lt;/div&gt;
          &lt;div className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg"&gt;
            &lt;Zap className="h-12 w-12 text-purple-600 mb-4" /&gt;
            &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3"&gt;Lightning Fast&lt;/h3&gt;
            &lt;p className="text-gray-600 dark:text-gray-300"&gt;
              Optimized for speed with unlimited file sizes and instant peer discovery.
            &lt;/p&gt;
          &lt;/div&gt;
        &lt;/div&gt;

        &lt;div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8"&gt;
          &lt;h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 text-center"&gt;
            How It Works
          &lt;/h2&gt;
          &lt;div className="grid md:grid-cols-4 gap-6"&gt;
            &lt;div className="text-center"&gt;
              &lt;div className="bg-blue-100 dark:bg-blue-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"&gt;
                &lt;span className="text-2xl font-bold text-blue-600 dark:text-blue-400"&gt;1&lt;/span&gt;
              &lt;/div&gt;
              &lt;h4 className="font-semibold text-gray-900 dark:text-white mb-2"&gt;Create Account&lt;/h4&gt;
              &lt;p className="text-sm text-gray-600 dark:text-gray-300"&gt;Get your unique 6-digit ID instantly&lt;/p&gt;
            &lt;/div&gt;
            &lt;div className="text-center"&gt;
              &lt;div className="bg-green-100 dark:bg-green-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"&gt;
                &lt;span className="text-2xl font-bold text-green-600 dark:text-green-400"&gt;2&lt;/span&gt;
              &lt;/div&gt;
              &lt;h4 className="font-semibold text-gray-900 dark:text-white mb-2"&gt;Upload Files&lt;/h4&gt;
              &lt;p className="text-sm text-gray-600 dark:text-gray-300"&gt;Drag &amp; drop any file type&lt;/p&gt;
            &lt;/div&gt;
            &lt;div className="text-center"&gt;
              &lt;div className="bg-purple-100 dark:bg-purple-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"&gt;
                &lt;span className="text-2xl font-bold text-purple-600 dark:text-purple-400"&gt;3&lt;/span&gt;
              &lt;/div&gt;
              &lt;h4 className="font-semibold text-gray-900 dark:text-white mb-2"&gt;Share Securely&lt;/h4&gt;
              &lt;p className="text-sm text-gray-600 dark:text-gray-300"&gt;Public or private sharing options&lt;/p&gt;
            &lt;/div&gt;
            &lt;div className="text-center"&gt;
              &lt;div className="bg-orange-100 dark:bg-orange-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"&gt;
                &lt;span className="text-2xl font-bold text-orange-600 dark:text-orange-400"&gt;4&lt;/span&gt;
              &lt;/div&gt;
              &lt;h4 className="font-semibold text-gray-900 dark:text-white mb-2"&gt;Connect P2P&lt;/h4&gt;
              &lt;p className="text-sm text-gray-600 dark:text-gray-300"&gt;Direct transfers on local network&lt;/p&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/main&gt;

      &lt;!-- Footer --&gt;
      &lt;footer className="container mx-auto px-4 py-8 border-t border-gray-200 dark:border-gray-700"&gt;
        &lt;div className="text-center"&gt;
          &lt;p className="text-gray-600 dark:text-gray-400 mb-2"&gt;
            Created with ❤️ by &lt;span className="font-semibold text-blue-600 dark:text-blue-400"&gt;Shaik Mahammad Yaseen&lt;/span&gt;
          &lt;/p&gt;
          &lt;p className="text-sm text-gray-500 dark:text-gray-500"&gt;
            Vadukondi - Privacy-focused P2P file sharing platform
          &lt;/p&gt;
        &lt;/div&gt;
      &lt;/footer&gt;

      &lt;!-- Auth Modal --&gt;
      {mounted && showAuth && (
        &lt;AuthForm onClose={() =&gt; setShowAuth(false)} /&gt;
      )}
    &lt;/div&gt;
  )
}</div>

            <div class="file-header">📄 src/components/AuthForm.tsx - Authentication Component</div>
            <div class="file-content">'use client'

import { useState, useEffect } from 'react'
import { X, User, Lock, Eye, EyeOff } from 'lucide-react'

interface AuthFormProps {
  onClose: () =&gt; void
}

export default function AuthForm({ onClose }: AuthFormProps) {
  const [isLogin, setIsLogin] = useState(true)
  const [userId, setUserId] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [mounted, setMounted] = useState(false)

  useEffect(() =&gt; {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  const handleSubmit = async (e: React.FormEvent) =&gt; {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!isLogin && password !== confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    try {
      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register'
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, password }),
      })

      const data = await response.json()

      if (data.success) {
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth-token', data.token)
          window.location.href = '/dashboard'
        }
      } else {
        setError(data.error || 'Authentication failed')
      }
    } catch (error) {
      setError('Network error. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    &lt;div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"&gt;
      &lt;div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full"&gt;
        &lt;div className="flex items-center justify-between p-6 border-b"&gt;
          &lt;h2 className="text-2xl font-bold text-gray-900 dark:text-white"&gt;
            {isLogin ? 'Welcome Back' : 'Create Account'}
          &lt;/h2&gt;
          &lt;button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          &gt;
            &lt;X className="h-6 w-6" /&gt;
          &lt;/button&gt;
        &lt;/div&gt;

        &lt;form onSubmit={handleSubmit} className="p-6"&gt;
          {error && (
            &lt;div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded mb-4"&gt;
              {error}
            &lt;/div&gt;
          )}

          &lt;div className="mb-4"&gt;
            &lt;label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"&gt;
              6-Digit ID
            &lt;/label&gt;
            &lt;div className="relative"&gt;
              &lt;User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" /&gt;
              &lt;input
                type="text"
                value={userId}
                onChange={(e) =&gt; setUserId(e.target.value.replace(/\D/g, '').slice(0, 6))}
                placeholder="123456"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
                maxLength={6}
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;

          &lt;div className="mb-4"&gt;
            &lt;label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"&gt;
              Password
            &lt;/label&gt;
            &lt;div className="relative"&gt;
              &lt;Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" /&gt;
              &lt;input
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) =&gt; setPassword(e.target.value)}
                placeholder="Enter your password"
                className="w-full pl-10 pr-12 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
                minLength={8}
              /&gt;
              &lt;button
                type="button"
                onClick={() =&gt; setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              &gt;
                {showPassword ? &lt;EyeOff className="h-5 w-5" /&gt; : &lt;Eye className="h-5 w-5" /&gt;}
              &lt;/button&gt;
            &lt;/div&gt;
          &lt;/div&gt;

          {!isLogin && (
            &lt;div className="mb-6"&gt;
              &lt;label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"&gt;
                Confirm Password
              &lt;/label&gt;
              &lt;div className="relative"&gt;
                &lt;Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" /&gt;
                &lt;input
                  type="password"
                  value={confirmPassword}
                  onChange={(e) =&gt; setConfirmPassword(e.target.value)}
                  placeholder="Confirm your password"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required
                  minLength={8}
                /&gt;
              &lt;/div&gt;
            &lt;/div&gt;
          )}

          &lt;button
            type="submit"
            disabled={loading || userId.length !== 6 || password.length &lt; 8}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-4 rounded-lg transition-colors font-medium"
          &gt;
            {loading ? 'Processing...' : (isLogin ? 'Sign In' : 'Create Account')}
          &lt;/button&gt;

          &lt;div className="mt-4 text-center"&gt;
            &lt;button
              type="button"
              onClick={() =&gt; setIsLogin(!isLogin)}
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            &gt;
              {isLogin ? "Don't have an account? Sign up" : 'Already have an account? Sign in'}
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/form&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  )
}</div>

            <div class="file-header">📄 src/app/dashboard/page.tsx - Dashboard Component</div>
            <div class="file-content">'use client'

import { useEffect, useState } from 'react'
import { Share2, Upload, Users, Search, Settings, LogOut } from 'lucide-react'
import FileUpload from '@/components/FileUpload'
import NetworkScan from '@/components/NetworkScan'
import PublicFiles from '@/components/PublicFiles'

export default function Dashboard() {
  const [userId, setUserId] = useState&lt;string | null&gt;(null)
  const [mounted, setMounted] = useState(false)
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [showNetworkScan, setShowNetworkScan] = useState(false)
  const [showPublicFiles, setShowPublicFiles] = useState(false)

  useEffect(() =&gt; {
    setMounted(true)
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth-token')
      if (!token) {
        window.location.href = '/'
        return
      }

      try {
        const payload = JSON.parse(atob(token.split('.')[1]))
        setUserId(payload.userId)
      } catch (error) {
        console.error('Invalid token:', error)
        localStorage.removeItem('auth-token')
        window.location.href = '/'
      }
    }
  }, [])

  const handleLogout = () =&gt; {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth-token')
      window.location.href = '/'
    }
  }

  if (!mounted || !userId) {
    return (
      &lt;div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center"&gt;
        &lt;div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"&gt;&lt;/div&gt;
      &lt;/div&gt;
    )
  }

  return (
    &lt;div className="min-h-screen bg-gray-50 dark:bg-gray-900"&gt;
      &lt;header className="bg-white dark:bg-gray-800 shadow-sm border-b"&gt;
        &lt;div className="container mx-auto px-4 py-4"&gt;
          &lt;div className="flex items-center justify-between"&gt;
            &lt;div className="flex items-center space-x-3"&gt;
              &lt;Share2 className="h-8 w-8 text-blue-600" /&gt;
              &lt;div&gt;
                &lt;h1 className="text-2xl font-bold text-gray-900 dark:text-white"&gt;Vadukondi&lt;/h1&gt;
                &lt;p className="text-sm text-gray-600 dark:text-gray-400"&gt;ID: {userId}&lt;/p&gt;
              &lt;/div&gt;
            &lt;/div&gt;
            &lt;div className="flex items-center space-x-4"&gt;
              &lt;button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"&gt;
                &lt;Settings className="h-5 w-5" /&gt;
              &lt;/button&gt;
              &lt;button
                onClick={handleLogout}
                className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400"
              &gt;
                &lt;LogOut className="h-5 w-5" /&gt;
                &lt;span&gt;Logout&lt;/span&gt;
              &lt;/button&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/header&gt;

      &lt;main className="container mx-auto px-4 py-8"&gt;
        &lt;div className="mb-8"&gt;
          &lt;h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2"&gt;
            Welcome back!
          &lt;/h2&gt;
          &lt;p className="text-gray-600 dark:text-gray-400"&gt;
            Manage your files and connect with nearby users
          &lt;/p&gt;
        &lt;/div&gt;

        &lt;div className="grid md:grid-cols-3 gap-6 mb-8"&gt;
          &lt;div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border"&gt;
            &lt;div className="flex items-center space-x-3 mb-4"&gt;
              &lt;Upload className="h-8 w-8 text-blue-600" /&gt;
              &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-white"&gt;Upload Files&lt;/h3&gt;
            &lt;/div&gt;
            &lt;p className="text-gray-600 dark:text-gray-400 mb-4"&gt;
              Share files with the world or keep them private
            &lt;/p&gt;
            &lt;button
              onClick={() =&gt; setShowFileUpload(true)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
            &gt;
              Choose Files
            &lt;/button&gt;
          &lt;/div&gt;

          &lt;div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border"&gt;
            &lt;div className="flex items-center space-x-3 mb-4"&gt;
              &lt;Users className="h-8 w-8 text-green-600" /&gt;
              &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-white"&gt;Nearby Users&lt;/h3&gt;
            &lt;/div&gt;
            &lt;p className="text-gray-600 dark:text-gray-400 mb-4"&gt;
              Discover and connect with users on your network
            &lt;/p&gt;
            &lt;button
              onClick={() =&gt; setShowNetworkScan(true)}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors"
            &gt;
              Scan Network
            &lt;/button&gt;
          &lt;/div&gt;

          &lt;div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border"&gt;
            &lt;div className="flex items-center space-x-3 mb-4"&gt;
              &lt;Search className="h-8 w-8 text-purple-600" /&gt;
              &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-white"&gt;Public Files&lt;/h3&gt;
            &lt;/div&gt;
            &lt;p className="text-gray-600 dark:text-gray-400 mb-4"&gt;
              Browse and download files shared by the community
            &lt;/p&gt;
            &lt;button
              onClick={() =&gt; setShowPublicFiles(true)}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
            &gt;
              Browse Public
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;

        &lt;div className="grid md:grid-cols-2 gap-6"&gt;
          &lt;div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border"&gt;
            &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4"&gt;My Files&lt;/h3&gt;
            &lt;div className="text-center py-8 text-gray-500 dark:text-gray-400"&gt;
              &lt;Upload className="h-12 w-12 mx-auto mb-4 opacity-50" /&gt;
              &lt;p&gt;No files uploaded yet&lt;/p&gt;
              &lt;p className="text-sm"&gt;Upload your first file to get started&lt;/p&gt;
            &lt;/div&gt;
          &lt;/div&gt;

          &lt;div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border"&gt;
            &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4"&gt;Recent Activity&lt;/h3&gt;
            &lt;div className="text-center py-8 text-gray-500 dark:text-gray-400"&gt;
              &lt;Share2 className="h-12 w-12 mx-auto mb-4 opacity-50" /&gt;
              &lt;p&gt;No recent activity&lt;/p&gt;
              &lt;p className="text-sm"&gt;Your file sharing activity will appear here&lt;/p&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/main&gt;

      &lt;!-- Footer --&gt;
      &lt;footer className="container mx-auto px-4 py-6 border-t border-gray-200 dark:border-gray-700"&gt;
        &lt;div className="text-center"&gt;
          &lt;p className="text-sm text-gray-500 dark:text-gray-500"&gt;
            Created by &lt;span className="font-semibold text-blue-600 dark:text-blue-400"&gt;Shaik Mahammad Yaseen&lt;/span&gt; •
            Vadukondi v1.0
          &lt;/p&gt;
        &lt;/div&gt;
      &lt;/footer&gt;

      &lt;!-- Modals --&gt;
      {mounted && showFileUpload && (
        &lt;FileUpload onClose={() =&gt; setShowFileUpload(false)} /&gt;
      )}
      {mounted && showNetworkScan && (
        &lt;NetworkScan onClose={() =&gt; setShowNetworkScan(false)} /&gt;
      )}
      {mounted && showPublicFiles && (
        &lt;PublicFiles onClose={() =&gt; setShowPublicFiles(false)} /&gt;
      )}
    &lt;/div&gt;
  )
}</div>
        </div>

        <div class="section">
            <h2>🔧 Backend API Routes</h2>

            <div class="file-header">📄 src/app/api/auth/register/route.ts - User Registration</div>
            <div class="file-content">import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { connectDB } from '@/lib/database'
import User from '@/models/User'
import { memoryStore } from '@/lib/memory-store'
import { generateUserId, isValidUserId } from '@/lib/utils'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

export async function POST(request: NextRequest) {
  try {
    const { userId, password } = await request.json()

    if (!userId || !password) {
      return NextResponse.json(
        { success: false, error: 'User ID and password are required' },
        { status: 400 }
      )
    }

    if (!isValidUserId(userId)) {
      return NextResponse.json(
        { success: false, error: 'User ID must be exactly 6 digits' },
        { status: 400 }
      )
    }

    if (password.length &lt; 8) {
      return NextResponse.json(
        { success: false, error: 'Password must be at least 8 characters' },
        { status: 400 }
      )
    }

    const hashedPassword = await bcrypt.hash(password, 12)

    try {
      await connectDB()

      const existingUser = await User.findOne({ userId })
      if (existingUser) {
        return NextResponse.json(
          { success: false, error: 'User ID already exists' },
          { status: 409 }
        )
      }

      const user = new User({
        userId,
        password: hashedPassword,
        createdAt: new Date()
      })

      await user.save()
      console.log('User created in MongoDB:', userId)

    } catch (dbError) {
      console.warn('MongoDB not available, using memory store:', dbError)

      const existingUser = await memoryStore.findUser(userId)
      if (existingUser) {
        return NextResponse.json(
          { success: false, error: 'User ID already exists' },
          { status: 409 }
        )
      }

      await memoryStore.createUser({
        userId,
        password: hashedPassword,
        createdAt: new Date().toISOString()
      })
      console.log('User created in memory store:', userId)
    }

    const token = jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' })

    return NextResponse.json({
      success: true,
      message: 'User registered successfully',
      token,
      userId
    })

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { success: false, error: 'Registration failed' },
      { status: 500 }
    )
  }
}</div>

            <div class="file-header">📄 src/app/api/auth/login/route.ts - User Login</div>
            <div class="file-content">import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { connectDB } from '@/lib/database'
import User from '@/models/User'
import { memoryStore } from '@/lib/memory-store'
import { isValidUserId } from '@/lib/utils'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

export async function POST(request: NextRequest) {
  try {
    const { userId, password } = await request.json()

    if (!userId || !password) {
      return NextResponse.json(
        { success: false, error: 'User ID and password are required' },
        { status: 400 }
      )
    }

    if (!isValidUserId(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID format' },
        { status: 400 }
      )
    }

    let user = null

    try {
      await connectDB()
      user = await User.findOne({ userId })
      console.log('User lookup in MongoDB:', userId, user ? 'found' : 'not found')
    } catch (dbError) {
      console.warn('MongoDB not available, using memory store:', dbError)
      user = await memoryStore.findUser(userId)
      console.log('User lookup in memory store:', userId, user ? 'found' : 'not found')
    }

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    const token = jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' })

    return NextResponse.json({
      success: true,
      message: 'Login successful',
      token,
      userId
    })

  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { success: false, error: 'Login failed' },
      { status: 500 }
    )
  }
}</div>
        </div>

        <div class="section">
            <h2>📦 Installation & Setup</h2>

            <div class="code-block">
# Clone the repository
git clone https://github.com/your-username/vadukondi.git
cd vadukondi

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Configure your environment variables in .env.local:
MONGODB_URI=mongodb://localhost:27017/vadukondi
JWT_SECRET=your-super-secret-jwt-key
OPENROUTER_API_KEY=your-openrouter-api-key

# Run the development server
npm run dev

# Open your browser and navigate to:
http://localhost:3000
            </div>

            <h3>🔧 Environment Variables</h3>
            <div class="code-block">
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/vadukondi

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# AI Integration (Optional)
OPENROUTER_API_KEY=your-openrouter-api-key

# Development
NODE_ENV=development
            </div>

            <h3>📋 Available Scripts</h3>
            <div class="code-block">
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript type checking
            </div>
        </div>

        <div class="section">
            <h2>🚀 Features Implemented</h2>

            <div class="feature-list">
                <div class="feature-item">
                    <h4>✅ Anonymous Authentication</h4>
                    <p>6-digit ID system with JWT tokens and bcrypt password hashing</p>
                </div>
                <div class="feature-item">
                    <h4>✅ Responsive Dashboard</h4>
                    <p>Modern UI with dark mode support and mobile optimization</p>
                </div>
                <div class="feature-item">
                    <h4>✅ File Upload System</h4>
                    <p>Drag-and-drop interface with progress tracking and validation</p>
                </div>
                <div class="feature-item">
                    <h4>✅ Network Discovery</h4>
                    <p>WebRTC-based peer discovery for local network sharing</p>
                </div>
                <div class="feature-item">
                    <h4>✅ Public File Browser</h4>
                    <p>Search and filter public files with category organization</p>
                </div>
                <div class="feature-item">
                    <h4>✅ Fallback Storage</h4>
                    <p>Memory store system for development without MongoDB</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔮 Future Enhancements</h2>

            <div class="feature-list">
                <div class="feature-item">
                    <h4>🔄 Real-time P2P Transfers</h4>
                    <p>Direct device-to-device file transfers using WebRTC data channels</p>
                </div>
                <div class="feature-item">
                    <h4>🤖 AI-Powered Features</h4>
                    <p>OpenRouter integration for file categorization and content analysis</p>
                </div>
                <div class="feature-item">
                    <h4>👥 Group Sharing</h4>
                    <p>Private groups with collaborative file sharing capabilities</p>
                </div>
                <div class="feature-item">
                    <h4>🔍 Advanced Search</h4>
                    <p>Semantic search with filters and intelligent recommendations</p>
                </div>
                <div class="feature-item">
                    <h4>🔐 Enhanced Security</h4>
                    <p>End-to-end encryption and advanced security measures</p>
                </div>
                <div class="feature-item">
                    <h4>📊 Analytics Dashboard</h4>
                    <p>Usage statistics and file sharing insights</p>
                </div>
            </div>
        </div>

        <div class="creator">
            <h2>🎯 Project Summary</h2>
            <p><strong>Vadukondi</strong> represents a modern approach to file sharing that prioritizes user privacy and security. Built by <strong>Shaik Mahammad Yaseen</strong>, this platform demonstrates expertise in full-stack development, modern web technologies, and privacy-focused design principles.</p>
            <br>
            <p>The project showcases advanced React patterns, secure authentication, responsive design, and scalable architecture - making it a comprehensive example of modern web application development.</p>
        </div>

        <div class="section" style="text-align: center; margin-top: 40px;">
            <button onclick="window.print()" class="tech-item" style="background: #059669; padding: 12px 24px; font-size: 1.1em; cursor: pointer; border: none; border-radius: 8px;">
                📄 Download as PDF
            </button>
            <p style="margin-top: 20px; color: #6b7280;">
                Click the button above to save this documentation as a PDF file
            </p>
        </div>
    </div>

    <script>
        // Add syntax highlighting and interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Add copy buttons to code blocks
            const codeBlocks = document.querySelectorAll('.file-content, .code-block');
            codeBlocks.forEach(block => {
                const copyBtn = document.createElement('button');
                copyBtn.innerHTML = '📋 Copy';
                copyBtn.style.cssText = 'position: absolute; top: 10px; right: 10px; background: #2563eb; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 0.8em;';

                const wrapper = document.createElement('div');
                wrapper.style.position = 'relative';
                block.parentNode.insertBefore(wrapper, block);
                wrapper.appendChild(block);
                wrapper.appendChild(copyBtn);

                copyBtn.addEventListener('click', () => {
                    navigator.clipboard.writeText(block.textContent);
                    copyBtn.innerHTML = '✅ Copied!';
                    setTimeout(() => copyBtn.innerHTML = '📋 Copy', 2000);
                });
            });
        });
    </script>
</body>
</html>
