import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/database'
import File from '@/models/File'
import { getUserFromRequest } from '@/lib/auth'
import memoryStore from '@/lib/memory-store'

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const user = getUserFromRequest(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    let files: any[] = []

    try {
      // Try to connect to MongoDB
      await connectDB()

      // Get public files from MongoDB
      files = await File.find({ isPublic: true })
        .sort({ uploadedAt: -1 })
        .limit(50)
        .lean()
    } catch (dbError) {
      console.warn('MongoDB not available, using memory store:', dbError)
      
      // Fallback to memory store
      files = await memoryStore.getPublicFiles()
    }

    // Transform files for frontend
    const transformedFiles = files.map(file => ({
      id: file._id?.toString() || file.id,
      name: file.name,
      size: file.size,
      type: file.type,
      uploaderId: file.uploaderId,
      uploadedAt: file.uploadedAt,
      downloads: file.downloads || 0,
      category: file.category || 'other'
    }))

    return NextResponse.json({
      success: true,
      files: transformedFiles
    })

  } catch (error) {
    console.error('Public files fetch error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
