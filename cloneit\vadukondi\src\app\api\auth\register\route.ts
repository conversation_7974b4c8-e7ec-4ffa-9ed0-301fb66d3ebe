import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/database'
import User from '@/models/User'
import { hashPassword, generateToken } from '@/lib/auth'
import { isValidUserId } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const { userId, password } = await request.json()

    // Validate input
    if (!userId || !password) {
      return NextResponse.json(
        { success: false, error: 'User ID and password are required' },
        { status: 400 }
      )
    }

    if (!isValidUserId(userId)) {
      return NextResponse.json(
        { success: false, error: 'User ID must be exactly 6 digits' },
        { status: 400 }
      )
    }

    if (password.length < 6) {
      return NextResponse.json(
        { success: false, error: 'Password must be at least 6 characters' },
        { status: 400 }
      )
    }

    // Connect to database
    await connectDB()

    // Check if user ID already exists
    const existingUser = await User.findOne({ id: userId })
    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'User ID already taken. Please choose another.' },
        { status: 409 }
      )
    }

    // Hash password and create user
    const passwordHash = await hashPassword(password)
    const user = new User({
      id: userId,
      passwordHash,
      createdAt: new Date(),
      lastActive: new Date()
    })

    await user.save()

    // Generate JWT token
    const token = generateToken(userId)

    return NextResponse.json({
      success: true,
      message: 'Account created successfully',
      token,
      userId
    })

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
