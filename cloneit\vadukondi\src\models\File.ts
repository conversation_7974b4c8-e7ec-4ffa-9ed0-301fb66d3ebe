import mongoose, { Schema, Document } from 'mongoose'
import { FileMetadata } from '@/types'

export interface FileDocument extends FileMetadata, Document {}

const FileSchema = new Schema<FileDocument>({
  id: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  name: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  type: {
    type: String,
    required: true
  },
  uploaderId: {
    type: String,
    required: true,
    match: /^\d{6}$/,
    index: true
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  isPublic: {
    type: Boolean,
    default: false,
    index: true
  },
  description: {
    type: String,
    maxlength: 1000
  },
  tags: [{
    type: String,
    maxlength: 50
  }],
  category: {
    type: String,
    maxlength: 100
  },
  summary: {
    type: String,
    maxlength: 500
  },
  downloadCount: {
    type: Number,
    default: 0
  },
  downloads: {
    type: Number,
    default: 0
  },
  groupId: {
    type: String,
    index: true
  },
  data: {
    type: <PERSON>uffer,
    required: false // Optional for large files that might be stored elsewhere
  }
}, {
  timestamps: true
})

// Compound indexes for efficient queries
FileSchema.index({ uploaderId: 1, uploadedAt: -1 })
FileSchema.index({ isPublic: 1, uploadedAt: -1 })
FileSchema.index({ groupId: 1, uploadedAt: -1 })
FileSchema.index({ tags: 1 })
FileSchema.index({ category: 1 })

export default mongoose.models.File || mongoose.model<FileDocument>('File', FileSchema)
