'use client'

import { useState, useEffect } from 'react'
import { Shield, Share2, Users, Zap } from 'lucide-react'
import AuthForm from '@/components/AuthForm'

export default function Home() {
  const [showAuth, setShowAuth] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Share2 className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">Vadukondi</span>
          </div>
          <button
            onClick={() => setShowAuth(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Get Started
          </button>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Secure & Seamless
            <span className="text-blue-600 block">P2P File Sharing</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Privacy-focused file sharing with anonymous 6-digit IDs. Share any file type instantly
            with direct peer-to-peer transfers and complete anonymity.
          </p>
          <button
            onClick={() => setShowAuth(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors"
          >
            Start Sharing Now
          </button>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mt-20">
          <div className="text-center p-6">
            <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Complete Privacy
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Anonymous 6-digit IDs, no email required, end-to-end encryption
            </p>
          </div>
          <div className="text-center p-6">
            <Zap className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Lightning Fast
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Direct P2P transfers, drag & drop interface, transfers in seconds
            </p>
          </div>
          <div className="text-center p-6">
            <Share2 className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Universal Support
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Any file type, unlimited size, works across all devices
            </p>
          </div>
          <div className="text-center p-6">
            <Users className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Smart Sharing
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Local network discovery, public feeds, private groups
            </p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="container mx-auto px-4 py-8 border-t border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-400 mb-2">
            Created with ❤️ by <span className="font-semibold text-blue-600 dark:text-blue-400">Shaik Mahammad Yaseen</span>
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-500">
            Vadukondi - Privacy-focused P2P file sharing platform
          </p>
        </div>
      </footer>

      {/* Auth Modal */}
      {mounted && showAuth && (
        <AuthForm onClose={() => setShowAuth(false)} />
      )}
    </div>
  )
}
