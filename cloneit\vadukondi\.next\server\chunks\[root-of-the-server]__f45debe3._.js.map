{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/database.ts"], "sourcesContent": ["import mongoose from 'mongoose'\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/vadukondi'\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable')\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null\n  promise: Promise<typeof mongoose> | null\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined\n}\n\nlet cached = global.mongoose\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null }\n}\n\nasync function connectDB() {\n  if (cached!.conn) {\n    return cached!.conn\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    }\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts)\n  }\n\n  try {\n    cached!.conn = await cached!.promise\n  } catch (e) {\n    cached!.promise = null\n    throw e\n  }\n\n  return cached!.conn\n}\n\nexport default connectDB\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAWA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa;IAClD;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/models/User.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose'\nimport { User } from '@/types'\n\nexport interface UserDocument extends User, Document {}\n\nconst UserSchema = new Schema<UserDocument>({\n  id: {\n    type: String,\n    required: true,\n    unique: true,\n    match: /^\\d{6}$/,\n    index: true\n  },\n  passwordHash: {\n    type: String,\n    required: true\n  },\n  createdAt: {\n    type: Date,\n    default: Date.now\n  },\n  lastActive: {\n    type: Date,\n    default: Date.now\n  }\n}, {\n  timestamps: true\n})\n\n// Ensure the 6-digit ID is unique\nUserSchema.index({ id: 1 }, { unique: true })\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema)\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,IAAI;QACF,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;QACP,OAAO;IACT;IACA,cAAc;QACZ,MAAM;QACN,UAAU;IACZ;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,YAAY;QACV,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;AACF,GAAG;IACD,YAAY;AACd;AAEA,kCAAkC;AAClC,WAAW,KAAK,CAAC;IAAE,IAAI;AAAE,GAAG;IAAE,QAAQ;AAAK;uCAE5B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production'\nconst SALT_ROUNDS = 12\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, SALT_ROUNDS)\n}\n\nexport async function verifyPassword(password: string, hash: string): Promise<boolean> {\n  return bcrypt.compare(password, hash)\n}\n\nexport function generateToken(userId: string): string {\n  return jwt.sign(\n    { userId, iat: Date.now() },\n    JWT_SECRET,\n    { expiresIn: '7d' }\n  )\n}\n\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string }\n    return decoded\n  } catch (error) {\n    return null\n  }\n}\n\nexport function extractTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7)\n  }\n  \n  // Also check cookies\n  const token = request.cookies.get('auth-token')?.value\n  return token || null\n}\n\nexport function getUserFromRequest(request: NextRequest): { userId: string } | null {\n  const token = extractTokenFromRequest(request)\n  if (!token) return null\n  \n  return verifyToken(token)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,cAAc;AAEb,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,IAAY;IACjE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CACb;QAAE;QAAQ,KAAK,KAAK,GAAG;IAAG,GAC1B,YACA;QAAE,WAAW;IAAK;AAEtB;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,wBAAwB,OAAoB;IAC1D,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,qBAAqB;IACrB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,QAAQ,wBAAwB;IACtC,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Generate a random 6-digit ID\nexport function generateUserId(): string {\n  return Math.floor(Math.random() * 1000000).toString().padStart(6, '0')\n}\n\n// Validate 6-digit ID format\nexport function isValidUserId(id: string): boolean {\n  return /^\\d{6}$/.test(id)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Generate unique file ID\nexport function generateFileId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2)\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG;AACpE;AAGO,SAAS,cAAc,EAAU;IACtC,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS;IACd,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport connectDB from '@/lib/database'\nimport User from '@/models/User'\nimport { hashPassword, generateToken } from '@/lib/auth'\nimport { isValidUserId } from '@/lib/utils'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId, password } = await request.json()\n\n    // Validate input\n    if (!userId || !password) {\n      return NextResponse.json(\n        { success: false, error: 'User ID and password are required' },\n        { status: 400 }\n      )\n    }\n\n    if (!isValidUserId(userId)) {\n      return NextResponse.json(\n        { success: false, error: 'User ID must be exactly 6 digits' },\n        { status: 400 }\n      )\n    }\n\n    if (password.length < 6) {\n      return NextResponse.json(\n        { success: false, error: 'Password must be at least 6 characters' },\n        { status: 400 }\n      )\n    }\n\n    // Connect to database\n    await connectDB()\n\n    // Check if user ID already exists\n    const existingUser = await User.findOne({ id: userId })\n    if (existingUser) {\n      return NextResponse.json(\n        { success: false, error: 'User ID already taken. Please choose another.' },\n        { status: 409 }\n      )\n    }\n\n    // Hash password and create user\n    const passwordHash = await hashPassword(password)\n    const user = new User({\n      id: userId,\n      passwordHash,\n      createdAt: new Date(),\n      lastActive: new Date()\n    })\n\n    await user.save()\n\n    // Generate JWT token\n    const token = generateToken(userId)\n\n    return NextResponse.json({\n      success: true,\n      message: 'Account created successfully',\n      token,\n      userId\n    })\n\n  } catch (error) {\n    console.error('Registration error:', error)\n    return NextResponse.json(\n      { success: false, error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/C,iBAAiB;QACjB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoC,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAmC,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyC,GAClE;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,CAAA,GAAA,wHAAA,CAAA,UAAS,AAAD;QAEd,kCAAkC;QAClC,MAAM,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,IAAI;QAAO;QACrD,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAgD,GACzE;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,eAAe,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;QACxC,MAAM,OAAO,IAAI,uHAAA,CAAA,UAAI,CAAC;YACpB,IAAI;YACJ;YACA,WAAW,IAAI;YACf,YAAY,IAAI;QAClB;QAEA,MAAM,KAAK,IAAI;QAEf,qBAAqB;QACrB,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE;QAE5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}