// Simple in-memory store for development/testing when MongoDB is not available
interface MemoryUser {
  id: string
  passwordHash: string
  createdAt: Date
  lastActive: Date
}

interface MemoryFile {
  id: string
  name: string
  size: number
  type: string
  uploaderId: string
  uploadedAt: Date
  downloads: number
  category: string
  isPublic: boolean
  data?: Buffer
}

class MemoryStore {
  private users: Map<string, MemoryUser> = new Map()
  private files: Map<string, MemoryFile> = new Map()

  async createUser(userData: MemoryUser): Promise<MemoryUser> {
    if (this.users.has(userData.id)) {
      throw new Error('User ID already exists')
    }
    this.users.set(userData.id, userData)
    return userData
  }

  async findUser(id: string): Promise<MemoryUser | null> {
    return this.users.get(id) || null
  }

  async updateUser(id: string, updates: Partial<MemoryUser>): Promise<MemoryUser | null> {
    const user = this.users.get(id)
    if (!user) return null
    
    const updatedUser = { ...user, ...updates }
    this.users.set(id, updatedUser)
    return updatedUser
  }

  async deleteUser(id: string): Promise<boolean> {
    return this.users.delete(id)
  }

  async getAllUsers(): Promise<MemoryUser[]> {
    return Array.from(this.users.values())
  }

  async createFile(fileData: MemoryFile): Promise<MemoryFile> {
    this.files.set(fileData.id, fileData)
    return fileData
  }

  async findFile(fileId: string): Promise<MemoryFile | null> {
    return this.files.get(fileId) || null
  }

  async getPublicFiles(): Promise<MemoryFile[]> {
    return Array.from(this.files.values())
      .filter(file => file.isPublic)
      .sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime())
      .slice(0, 50)
  }

  async getUserFiles(userId: string): Promise<MemoryFile[]> {
    return Array.from(this.files.values())
      .filter(file => file.uploaderId === userId)
      .sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime())
  }

  async deleteFile(fileId: string): Promise<boolean> {
    return this.files.delete(fileId)
  }

  clear(): void {
    this.users.clear()
    this.files.clear()
  }
}

// Global instance
const memoryStore = new MemoryStore()

export default memoryStore
export type { MemoryUser, MemoryFile }
