'use client'

import { useEffect, useState } from 'react'
import { Share2, Upload, Users, Search, Settings, LogOut } from 'lucide-react'
import FileUpload from '@/components/FileUpload'
import NetworkScan from '@/components/NetworkScan'
import PublicFiles from '@/components/PublicFiles'

export default function Dashboard() {
  const [userId, setUserId] = useState<string | null>(null)
  const [mounted, setMounted] = useState(false)
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [showNetworkScan, setShowNetworkScan] = useState(false)
  const [showPublicFiles, setShowPublicFiles] = useState(false)

  useEffect(() => {
    setMounted(true)
    // Check if user is authenticated
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth-token')
      if (!token) {
        window.location.href = '/'
        return
      }
      
      // TODO: Verify token and get user info
      // For now, we'll extract userId from token or set a placeholder
      setUserId('123456') // Placeholder
    }
  }, [])

  const handleLogout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth-token')
      window.location.href = '/'
    }
  }

  if (!mounted) {
    return null
  }

  if (!userId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Share2 className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Vadukondi</h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">ID: {userId}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                <Settings className="h-5 w-5" />
              </button>
              <button 
                onClick={handleLogout}
                className="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Quick Actions */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border">
            <div className="flex items-center space-x-3 mb-4">
              <Upload className="h-8 w-8 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Upload Files</h2>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Drag and drop files or click to upload. Share instantly with anyone.
            </p>
            <button
              onClick={() => setShowFileUpload(true)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Choose Files
            </button>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border">
            <div className="flex items-center space-x-3 mb-4">
              <Users className="h-8 w-8 text-green-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Nearby Users</h2>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Discover and share files with users on your local network.
            </p>
            <button
              onClick={() => setShowNetworkScan(true)}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Scan Network
            </button>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border">
            <div className="flex items-center space-x-3 mb-4">
              <Search className="h-8 w-8 text-purple-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Public Files</h2>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Browse and download files shared publicly by the community.
            </p>
            <button
              onClick={() => setShowPublicFiles(true)}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Browse Public
            </button>
          </div>
        </div>

        {/* File Management Sections */}
        <div className="grid lg:grid-cols-2 gap-8">
          {/* My Files */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">My Files</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Files you've uploaded</p>
            </div>
            <div className="p-6">
              <div className="text-center py-8">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">No files uploaded yet</p>
                <p className="text-sm text-gray-500 dark:text-gray-500">Upload your first file to get started</p>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Your sharing history</p>
            </div>
            <div className="p-6">
              <div className="text-center py-8">
                <Share2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">No activity yet</p>
                <p className="text-sm text-gray-500 dark:text-gray-500">Start sharing files to see your activity</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="container mx-auto px-4 py-6 border-t border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <p className="text-sm text-gray-500 dark:text-gray-500">
            Created by <span className="font-semibold text-blue-600 dark:text-blue-400">Shaik Mahammad Yaseen</span> •
            Vadukondi v1.0
          </p>
        </div>
      </footer>

      {/* Modals */}
      {mounted && showFileUpload && (
        <FileUpload onClose={() => setShowFileUpload(false)} />
      )}
      {mounted && showNetworkScan && (
        <NetworkScan onClose={() => setShowNetworkScan(false)} />
      )}
      {mounted && showPublicFiles && (
        <PublicFiles onClose={() => setShowPublicFiles(false)} />
      )}
    </div>
  )
}
