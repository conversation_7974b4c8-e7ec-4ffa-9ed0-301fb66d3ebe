{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/database.ts"], "sourcesContent": ["import mongoose from 'mongoose'\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/vadukondi'\n\nif (!MONGODB_URI) {\n  console.warn('MONGODB_URI not defined, using default local MongoDB connection')\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null\n  promise: Promise<typeof mongoose> | null\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined\n}\n\nlet cached = global.mongoose\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null }\n}\n\nasync function connectDB() {\n  if (cached!.conn) {\n    return cached!.conn\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n      serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s\n      socketTimeoutMS: 45000,\n    }\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts)\n  }\n\n  try {\n    cached!.conn = await cached!.promise\n  } catch (e) {\n    cached!.promise = null\n    console.error('MongoDB connection error:', e)\n    throw new Error('Failed to connect to MongoDB. Please ensure MongoDB is running.')\n  }\n\n  return cached!.conn\n}\n\nexport default connectDB\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAWA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;YAChB,0BAA0B;YAC1B,iBAAiB;QACnB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa;IAClD;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/models/User.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose'\nimport { User } from '@/types'\n\nexport interface UserDocument extends User, Document {}\n\nconst UserSchema = new Schema<UserDocument>({\n  id: {\n    type: String,\n    required: true,\n    unique: true,\n    match: /^\\d{6}$/\n  },\n  passwordHash: {\n    type: String,\n    required: true\n  },\n  createdAt: {\n    type: Date,\n    default: Date.now\n  },\n  lastActive: {\n    type: Date,\n    default: Date.now\n  }\n}, {\n  timestamps: true\n})\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema)\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,IAAI;QACF,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,cAAc;QACZ,MAAM;QACN,UAAU;IACZ;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,YAAY;QACV,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;AACF,GAAG;IACD,YAAY;AACd;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production'\nconst SALT_ROUNDS = 12\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, SALT_ROUNDS)\n}\n\nexport async function verifyPassword(password: string, hash: string): Promise<boolean> {\n  return bcrypt.compare(password, hash)\n}\n\nexport function generateToken(userId: string): string {\n  return jwt.sign(\n    { userId, iat: Date.now() },\n    JWT_SECRET,\n    { expiresIn: '7d' }\n  )\n}\n\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string }\n    return decoded\n  } catch (error) {\n    return null\n  }\n}\n\nexport function extractTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7)\n  }\n  \n  // Also check cookies\n  const token = request.cookies.get('auth-token')?.value\n  return token || null\n}\n\nexport function getUserFromRequest(request: NextRequest): { userId: string } | null {\n  const token = extractTokenFromRequest(request)\n  if (!token) return null\n  \n  return verifyToken(token)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,cAAc;AAEb,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,IAAY;IACjE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CACb;QAAE;QAAQ,KAAK,KAAK,GAAG;IAAG,GAC1B,YACA;QAAE,WAAW;IAAK;AAEtB;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,wBAAwB,OAAoB;IAC1D,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,qBAAqB;IACrB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,QAAQ,wBAAwB;IACtC,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Generate a random 6-digit ID\nexport function generateUserId(): string {\n  return Math.floor(Math.random() * 1000000).toString().padStart(6, '0')\n}\n\n// Validate 6-digit ID format\nexport function isValidUserId(id: string): boolean {\n  return /^\\d{6}$/.test(id)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Generate unique file ID\nexport function generateFileId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2)\n}\n\n// Format date string\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString()\n}\n\n// Get file category from MIME type\nexport function getFileCategory(mimeType: string): string {\n  if (mimeType.startsWith('image/')) return 'image'\n  if (mimeType.startsWith('video/')) return 'video'\n  if (mimeType.startsWith('audio/')) return 'audio'\n  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) return 'archive'\n  if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) return 'document'\n  return 'other'\n}\n\n// Validate user ID (alias for backward compatibility)\nexport function validateUserId(userId: string): boolean {\n  return isValidUserId(userId)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG;AACpE;AAGO,SAAS,cAAc,EAAU;IACtC,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS;IACd,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB;AAChD;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,SAAS,QAAQ,CAAC,UAAU,SAAS,QAAQ,CAAC,UAAU,SAAS,QAAQ,CAAC,QAAQ,OAAO;IAC7F,IAAI,SAAS,QAAQ,CAAC,UAAU,SAAS,QAAQ,CAAC,eAAe,SAAS,QAAQ,CAAC,SAAS,OAAO;IACnG,OAAO;AACT;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,cAAc;AACvB", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/lib/memory-store.ts"], "sourcesContent": ["// Simple in-memory store for development/testing when MongoDB is not available\ninterface MemoryUser {\n  id: string\n  passwordHash: string\n  createdAt: Date\n  lastActive: Date\n}\n\nclass MemoryStore {\n  private users: Map<string, MemoryUser> = new Map()\n\n  async createUser(userData: MemoryUser): Promise<MemoryUser> {\n    if (this.users.has(userData.id)) {\n      throw new Error('User ID already exists')\n    }\n    this.users.set(userData.id, userData)\n    return userData\n  }\n\n  async findUser(id: string): Promise<MemoryUser | null> {\n    return this.users.get(id) || null\n  }\n\n  async updateUser(id: string, updates: Partial<MemoryUser>): Promise<MemoryUser | null> {\n    const user = this.users.get(id)\n    if (!user) return null\n    \n    const updatedUser = { ...user, ...updates }\n    this.users.set(id, updatedUser)\n    return updatedUser\n  }\n\n  async deleteUser(id: string): Promise<boolean> {\n    return this.users.delete(id)\n  }\n\n  async getAllUsers(): Promise<MemoryUser[]> {\n    return Array.from(this.users.values())\n  }\n\n  clear(): void {\n    this.users.clear()\n  }\n}\n\n// Global instance\nconst memoryStore = new MemoryStore()\n\nexport default memoryStore\nexport type { MemoryUser }\n"], "names": [], "mappings": "AAAA,+EAA+E;;;;AAQ/E,MAAM;IACI,QAAiC,IAAI,MAAK;IAElD,MAAM,WAAW,QAAoB,EAAuB;QAC1D,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG;YAC/B,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE;QAC5B,OAAO;IACT;IAEA,MAAM,SAAS,EAAU,EAA8B;QACrD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;IAC/B;IAEA,MAAM,WAAW,EAAU,EAAE,OAA4B,EAA8B;QACrF,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,cAAc;YAAE,GAAG,IAAI;YAAE,GAAG,OAAO;QAAC;QAC1C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;QACnB,OAAO;IACT;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA,MAAM,cAAqC;QACzC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;IACrC;IAEA,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;AACF;AAEA,kBAAkB;AAClB,MAAM,cAAc,IAAI;uCAET", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport connectDB from '@/lib/database'\nimport User from '@/models/User'\nimport { verifyPassword, generateToken } from '@/lib/auth'\nimport { isValidUserId } from '@/lib/utils'\nimport memoryStore from '@/lib/memory-store'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId, password } = await request.json()\n\n    // Validate input\n    if (!userId || !password) {\n      return NextResponse.json(\n        { success: false, error: 'User ID and password are required' },\n        { status: 400 }\n      )\n    }\n\n    if (!isValidUserId(userId)) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid User ID format' },\n        { status: 400 }\n      )\n    }\n\n    let user: any = null\n\n    try {\n      // Try to connect to MongoDB\n      await connectDB()\n\n      // Find user in MongoDB\n      user = await User.findOne({ id: userId })\n\n      if (user) {\n        // Verify password\n        const isValidPassword = await verifyPassword(password, user.passwordHash)\n        if (!isValidPassword) {\n          return NextResponse.json(\n            { success: false, error: 'Invalid credentials' },\n            { status: 401 }\n          )\n        }\n\n        // Update last active\n        user.lastActive = new Date()\n        await user.save()\n      }\n    } catch (dbError) {\n      console.warn('MongoDB not available, using memory store:', dbError)\n      user = null\n    }\n\n    // If not found in MongoDB, try memory store\n    if (!user) {\n      user = await memoryStore.findUser(userId)\n      if (!user) {\n        return NextResponse.json(\n          { success: false, error: 'Invalid credentials' },\n          { status: 401 }\n        )\n      }\n\n      // Verify password\n      const isValidPassword = await verifyPassword(password, user.passwordHash)\n      if (!isValidPassword) {\n        return NextResponse.json(\n          { success: false, error: 'Invalid credentials' },\n          { status: 401 }\n        )\n      }\n\n      // Update last active in memory store\n      await memoryStore.updateUser(userId, { lastActive: new Date() })\n    }\n\n    // Generate JWT token\n    const token = generateToken(userId)\n\n    return NextResponse.json({\n      success: true,\n      message: 'Login successful',\n      token,\n      userId\n    })\n\n  } catch (error) {\n    console.error('Login error:', error)\n    return NextResponse.json(\n      { success: false, error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/C,iBAAiB;QACjB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoC,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,OAAY;QAEhB,IAAI;YACF,4BAA4B;YAC5B,MAAM,CAAA,GAAA,wHAAA,CAAA,UAAS,AAAD;YAEd,uBAAuB;YACvB,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAAE,IAAI;YAAO;YAEvC,IAAI,MAAM;gBACR,kBAAkB;gBAClB,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,KAAK,YAAY;gBACxE,IAAI,CAAC,iBAAiB;oBACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBAAE,SAAS;wBAAO,OAAO;oBAAsB,GAC/C;wBAAE,QAAQ;oBAAI;gBAElB;gBAEA,qBAAqB;gBACrB,KAAK,UAAU,GAAG,IAAI;gBACtB,MAAM,KAAK,IAAI;YACjB;QACF,EAAE,OAAO,SAAS;YAChB,QAAQ,IAAI,CAAC,8CAA8C;YAC3D,OAAO;QACT;QAEA,4CAA4C;QAC5C,IAAI,CAAC,MAAM;YACT,OAAO,MAAM,+HAAA,CAAA,UAAW,CAAC,QAAQ,CAAC;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAsB,GAC/C;oBAAE,QAAQ;gBAAI;YAElB;YAEA,kBAAkB;YAClB,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,KAAK,YAAY;YACxE,IAAI,CAAC,iBAAiB;gBACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAsB,GAC/C;oBAAE,QAAQ;gBAAI;YAElB;YAEA,qCAAqC;YACrC,MAAM,+HAAA,CAAA,UAAW,CAAC,UAAU,CAAC,QAAQ;gBAAE,YAAY,IAAI;YAAO;QAChE;QAEA,qBAAqB;QACrB,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE;QAE5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}