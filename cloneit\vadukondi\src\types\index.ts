// User types
export interface User {
  id: string // 6-digit ID
  passwordHash: string
  createdAt: Date
  lastActive: Date
}

// File types
export interface FileMetadata {
  id: string
  name: string
  size: number
  type: string
  uploaderId: string
  uploadedAt: Date
  isPublic: boolean
  description?: string
  tags?: string[]
  category?: string
  summary?: string
  downloadCount: number
  groupId?: string
}

// Sharing types
export interface ShareRequest {
  id: string
  senderId: string
  receiverId: string
  fileId: string
  status: 'pending' | 'accepted' | 'rejected' | 'completed'
  createdAt: Date
  expiresAt: Date
}

// Group types
export interface Group {
  id: string
  name: string
  creatorId: string
  members: string[]
  createdAt: Date
  isPrivate: boolean
}

// P2P types
export interface PeerConnection {
  userId: string
  isOnline: boolean
  lastSeen: Date
  isLocal: boolean
}

// WebRTC types
export interface RTCMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'file-request' | 'file-response'
  data: any
  senderId: string
  receiverId: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Search types
export interface SearchFilters {
  fileType?: string
  dateRange?: {
    start: Date
    end: Date
  }
  sizeRange?: {
    min: number
    max: number
  }
  uploaderId?: string
  tags?: string[]
  category?: string
}

export interface SearchResult {
  files: FileMetadata[]
  total: number
  page: number
  limit: number
}
