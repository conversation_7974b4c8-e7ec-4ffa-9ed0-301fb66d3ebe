import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Generate a random 6-digit ID
export function generateUserId(): string {
  return Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
}

// Validate 6-digit ID format
export function isValidUserId(id: string): boolean {
  return /^\d{6}$/.test(id)
}

// Format file size
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Generate unique file ID
export function generateFileId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}
