'use client'

import { useState, useEffect } from 'react'
import { X, Wifi, Users, Share2, Download, RefreshCw, Bluetooth } from 'lucide-react'

interface NetworkScanProps {
  onClose: () => void
}

interface NearbyDevice {
  id: string
  name: string
  type: 'bluetooth' | 'network'
  connected: boolean
  distance?: string
}

export default function NetworkScan({ onClose }: NetworkScanProps) {
  const [scanning, setScanning] = useState(false)
  const [nearbyDevices, setNearbyDevices] = useState<NearbyDevice[]>([])
  const [scanComplete, setScanComplete] = useState(false)
  const [bluetoothSupported, setBluetoothSupported] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    // Check if Bluetooth is supported
    if (typeof navigator !== 'undefined' && 'bluetooth' in navigator) {
      setBluetoothSupported(true)
    }
  }, [])

  const startBluetoothScan = async () => {
    if (!bluetoothSupported) {
      setError('Bluetooth is not supported in this browser')
      return
    }

    setScanning(true)
    setScanComplete(false)
    setNearbyDevices([])
    setError('')

    try {
      // Request Bluetooth device
      const device = await navigator.bluetooth.requestDevice({
        acceptAllDevices: true,
        optionalServices: ['battery_service', 'device_information']
      })

      if (device) {
        const newDevice: NearbyDevice = {
          id: device.id || `bt-${Date.now()}`,
          name: device.name || 'Unknown Device',
          type: 'bluetooth',
          connected: device.gatt?.connected || false
        }

        setNearbyDevices([newDevice])
        setScanComplete(true)
      }
    } catch (err: any) {
      if (err.name === 'NotFoundError') {
        setError('No Bluetooth devices found or user cancelled')
      } else if (err.name === 'NotAllowedError') {
        setError('Bluetooth access denied. Please allow Bluetooth permissions.')
      } else {
        setError(`Bluetooth error: ${err.message}`)
      }
    } finally {
      setScanning(false)
    }
  }

  const startNetworkScan = async () => {
    setScanning(true)
    setScanComplete(false)
    setNearbyDevices([])
    setError('')

    // Real network scanning would require WebRTC or server-side discovery
    // For now, we'll show a message about network discovery
    setTimeout(() => {
      setError('Network discovery requires WebRTC peer connection setup')
      setScanning(false)
      setScanComplete(true)
    }, 2000)
  }

  const connectToDevice = async (device: NearbyDevice) => {
    if (device.type === 'bluetooth') {
      try {
        const bluetoothDevice = await navigator.bluetooth.requestDevice({
          filters: [{ name: device.name }]
        })

        if (bluetoothDevice.gatt) {
          const server = await bluetoothDevice.gatt.connect()
          alert(`Connected to ${device.name}!`)

          // Update device status
          setNearbyDevices(prev =>
            prev.map(d =>
              d.id === device.id ? { ...d, connected: true } : d
            )
          )
        }
      } catch (err: any) {
        alert(`Failed to connect: ${err.message}`)
      }
    } else {
      alert('Network device connection not implemented yet')
    }
  }

  const shareFiles = (device: NearbyDevice) => {
    alert(`File sharing with ${device.name} would be implemented here`)
    // This would implement actual file transfer via Bluetooth or WebRTC
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Wifi className="h-6 w-6 text-blue-600" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Network Scan</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          {/* Error Display */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {/* Scan Status */}
          <div className="text-center mb-6">
            {scanning ? (
              <div>
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-lg font-medium text-gray-900 dark:text-white">
                  Scanning for devices...
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Looking for nearby Bluetooth and network devices
                </p>
              </div>
            ) : scanComplete ? (
              <div>
                <Users className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 dark:text-white">
                  Scan Complete
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Found {nearbyDevices.length} nearby devices
                </p>
              </div>
            ) : (
              <div>
                <Wifi className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 dark:text-white">
                  Device Discovery
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Scan for Bluetooth devices or network peers
                </p>
              </div>
            )}
          </div>

          {/* Scan Buttons */}
          <div className="flex gap-4 justify-center mb-6">
            <button
              onClick={startBluetoothScan}
              disabled={scanning || !bluetoothSupported}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Bluetooth className={`h-5 w-5 ${scanning ? 'animate-spin' : ''}`} />
              <span>Bluetooth</span>
            </button>
            <button
              onClick={startNetworkScan}
              disabled={scanning}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Wifi className={`h-5 w-5 ${scanning ? 'animate-spin' : ''}`} />
              <span>Network</span>
            </button>
          </div>

          {!bluetoothSupported && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-yellow-700 dark:text-yellow-400 px-4 py-3 rounded mb-4">
              Bluetooth is not supported in this browser. Try Chrome or Edge for Bluetooth functionality.
            </div>
          )}

          {/* Nearby Devices List */}
          {nearbyDevices.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Discovered Devices ({nearbyDevices.length})
              </h3>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {nearbyDevices.map((device) => (
                  <div key={device.id} className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="relative">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white">
                        {device.type === 'bluetooth' ? (
                          <Bluetooth className="h-6 w-6" />
                        ) : (
                          <Wifi className="h-6 w-6" />
                        )}
                      </div>
                      <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                        device.connected ? 'bg-green-500' : 'bg-gray-500'
                      }`}></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        {device.name}
                      </h4>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {device.type === 'bluetooth' ? 'Bluetooth Device' : 'Network Device'}
                        {device.distance && ` • ${device.distance} away`}
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Status: {device.connected ? 'Connected' : 'Available'}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => shareFiles(device)}
                        className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors"
                      >
                        <Share2 className="h-3 w-3" />
                        <span>Share</span>
                      </button>
                      <button
                        onClick={() => connectToDevice(device)}
                        disabled={device.connected}
                        className="flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-xs transition-colors"
                      >
                        <Download className="h-3 w-3" />
                        <span>{device.connected ? 'Connected' : 'Connect'}</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Help Text */}
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Real Device Discovery:</strong> Bluetooth scanning uses the Web Bluetooth API to find actual nearby devices.
              Network discovery would require WebRTC peer connections. Files can be transferred directly between connected devices.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
