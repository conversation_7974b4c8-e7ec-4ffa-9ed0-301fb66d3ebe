'use client'

import { useState, useEffect } from 'react'
import { X, Wifi, Users, Share2, Download, RefreshCw } from 'lucide-react'

interface NetworkScanProps {
  onClose: () => void
}

interface NearbyUser {
  id: string
  userId: string
  deviceName: string
  distance: string
  filesShared: number
  lastSeen: string
  status: 'online' | 'away' | 'offline'
}

export default function NetworkScan({ onClose }: NetworkScanProps) {
  const [scanning, setScanning] = useState(false)
  const [nearbyUsers, setNearbyUsers] = useState<NearbyUser[]>([])
  const [scanComplete, setScanComplete] = useState(false)

  // Mock data for demonstration
  const mockUsers: NearbyUser[] = [
    {
      id: 'user-789012',
      userId: '789012',
      deviceName: 'John\'s Laptop',
      distance: '2m',
      filesShared: 15,
      lastSeen: 'Just now',
      status: 'online'
    },
    {
      id: 'user-345678',
      userId: '345678',
      deviceName: 'Sarah\'s Phone',
      distance: '5m',
      filesShared: 8,
      lastSeen: '2 min ago',
      status: 'online'
    },
    {
      id: 'user-901234',
      userId: '901234',
      deviceName: 'Office Desktop',
      distance: '12m',
      filesShared: 23,
      lastSeen: '5 min ago',
      status: 'away'
    },
    {
      id: 'user-567890',
      userId: '567890',
      deviceName: 'Mike\'s Tablet',
      distance: '8m',
      filesShared: 4,
      lastSeen: '10 min ago',
      status: 'away'
    }
  ]

  const startScan = () => {
    setScanning(true)
    setScanComplete(false)
    setNearbyUsers([])

    // Simulate network scanning
    setTimeout(() => {
      // Add users one by one to simulate discovery
      mockUsers.forEach((user, index) => {
        setTimeout(() => {
          setNearbyUsers(prev => [...prev, user])
          if (index === mockUsers.length - 1) {
            setScanning(false)
            setScanComplete(true)
          }
        }, (index + 1) * 800)
      })
    }, 1000)
  }

  useEffect(() => {
    // Auto-start scan when component mounts
    startScan()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500'
      case 'away': return 'bg-yellow-500'
      case 'offline': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  const handleConnectToUser = (user: NearbyUser) => {
    alert(`Connecting to ${user.deviceName} (${user.userId})...`)
    // Here you would implement WebRTC connection logic
  }

  const handleRequestFiles = (user: NearbyUser) => {
    alert(`Requesting file list from ${user.deviceName}...`)
    // Here you would implement file list request logic
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Wifi className="h-6 w-6 text-blue-600" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Network Scan</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          {/* Scan Status */}
          <div className="text-center mb-6">
            {scanning ? (
              <div>
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-lg font-medium text-gray-900 dark:text-white">
                  Scanning for nearby devices...
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Looking for Vadukondi users on your network
                </p>
              </div>
            ) : scanComplete ? (
              <div>
                <Users className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 dark:text-white">
                  Scan Complete
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Found {nearbyUsers.length} nearby users
                </p>
              </div>
            ) : (
              <div>
                <Wifi className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 dark:text-white">
                  Ready to Scan
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Click scan to find nearby Vadukondi users
                </p>
              </div>
            )}
          </div>

          {/* Scan Button */}
          <div className="text-center mb-6">
            <button
              onClick={startScan}
              disabled={scanning}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg transition-colors mx-auto"
            >
              <RefreshCw className={`h-5 w-5 ${scanning ? 'animate-spin' : ''}`} />
              <span>{scanning ? 'Scanning...' : 'Scan Network'}</span>
            </button>
          </div>

          {/* Nearby Users List */}
          {nearbyUsers.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Nearby Users ({nearbyUsers.length})
              </h3>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {nearbyUsers.map((user) => (
                  <div key={user.id} className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="relative">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                        {user.userId.slice(0, 2)}
                      </div>
                      <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getStatusColor(user.status)}`}></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        {user.deviceName}
                      </h4>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        ID: {user.userId} • {user.distance} away
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {user.filesShared} files shared • {user.lastSeen}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleRequestFiles(user)}
                        className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors"
                      >
                        <Download className="h-3 w-3" />
                        <span>Files</span>
                      </button>
                      <button
                        onClick={() => handleConnectToUser(user)}
                        className="flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors"
                      >
                        <Share2 className="h-3 w-3" />
                        <span>Connect</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Help Text */}
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>How it works:</strong> Network scan uses WebRTC to discover other Vadukondi users 
              on your local network. Files are transferred directly between devices without going through our servers.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
