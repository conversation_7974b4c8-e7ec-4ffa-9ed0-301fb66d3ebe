import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/database'
import User from '@/models/User'
import { verifyPassword, generateToken } from '@/lib/auth'
import { isValidUserId } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const { userId, password } = await request.json()

    // Validate input
    if (!userId || !password) {
      return NextResponse.json(
        { success: false, error: 'User ID and password are required' },
        { status: 400 }
      )
    }

    if (!isValidUserId(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid User ID format' },
        { status: 400 }
      )
    }

    // Connect to database
    await connectDB()

    // Find user
    const user = await User.findOne({ id: userId })
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash)
    if (!isValidPassword) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Update last active
    user.lastActive = new Date()
    await user.save()

    // Generate JWT token
    const token = generateToken(userId)

    return NextResponse.json({
      success: true,
      message: 'Login successful',
      token,
      userId
    })

  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
