{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/components/FileUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useCallback } from 'react'\nimport { Upload, X, File, CheckCircle, AlertCircle } from 'lucide-react'\n\ninterface FileUploadProps {\n  onClose: () => void\n}\n\ninterface UploadFile {\n  file: File\n  id: string\n  progress: number\n  status: 'pending' | 'uploading' | 'completed' | 'error'\n  error?: string\n}\n\nexport default function FileUpload({ onClose }: FileUploadProps) {\n  const [files, setFiles] = useState<UploadFile[]>([])\n  const [isDragOver, setIsDragOver] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const generateFileId = () => Math.random().toString(36).substr(2, 9)\n\n  const addFiles = useCallback((newFiles: FileList | File[]) => {\n    const fileArray = Array.from(newFiles)\n    const uploadFiles: UploadFile[] = fileArray.map(file => ({\n      file,\n      id: generateFileId(),\n      progress: 0,\n      status: 'pending'\n    }))\n    setFiles(prev => [...prev, ...uploadFiles])\n  }, [])\n\n  const removeFile = (id: string) => {\n    setFiles(prev => prev.filter(f => f.id !== id))\n  }\n\n  const uploadFile = async (uploadFile: UploadFile) => {\n    setFiles(prev => prev.map(f => \n      f.id === uploadFile.id ? { ...f, status: 'uploading' } : f\n    ))\n\n    try {\n      const formData = new FormData()\n      formData.append('file', uploadFile.file)\n      formData.append('isPublic', 'false') // Default to private\n\n      // Simulate upload progress\n      const progressInterval = setInterval(() => {\n        setFiles(prev => prev.map(f => {\n          if (f.id === uploadFile.id && f.progress < 90) {\n            return { ...f, progress: f.progress + 10 }\n          }\n          return f\n        }))\n      }, 200)\n\n      const response = await fetch('/api/files/upload', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`\n        },\n        body: formData\n      })\n\n      clearInterval(progressInterval)\n\n      if (response.ok) {\n        setFiles(prev => prev.map(f => \n          f.id === uploadFile.id ? { ...f, progress: 100, status: 'completed' } : f\n        ))\n      } else {\n        const error = await response.text()\n        setFiles(prev => prev.map(f => \n          f.id === uploadFile.id ? { ...f, status: 'error', error } : f\n        ))\n      }\n    } catch (error) {\n      setFiles(prev => prev.map(f => \n        f.id === uploadFile.id ? { ...f, status: 'error', error: 'Upload failed' } : f\n      ))\n    }\n  }\n\n  const uploadAllFiles = () => {\n    files.filter(f => f.status === 'pending').forEach(uploadFile)\n  }\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(true)\n  }\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(false)\n  }\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(false)\n    if (e.dataTransfer.files) {\n      addFiles(e.dataTransfer.files)\n    }\n  }\n\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files) {\n      addFiles(e.target.files)\n    }\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Upload Files</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Drop Zone */}\n          <div\n            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n              isDragOver \n                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' \n                : 'border-gray-300 dark:border-gray-600'\n            }`}\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n          >\n            <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              Drop files here or click to browse\n            </p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n              Support for any file type, unlimited size\n            </p>\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors\"\n            >\n              Choose Files\n            </button>\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              multiple\n              onChange={handleFileSelect}\n              className=\"hidden\"\n            />\n          </div>\n\n          {/* File List */}\n          {files.length > 0 && (\n            <div className=\"mt-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Files ({files.length})\n                </h3>\n                <button\n                  onClick={uploadAllFiles}\n                  disabled={files.every(f => f.status !== 'pending')}\n                  className=\"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors\"\n                >\n                  Upload All\n                </button>\n              </div>\n\n              <div className=\"max-h-60 overflow-y-auto space-y-2\">\n                {files.map((uploadFile) => (\n                  <div key={uploadFile.id} className=\"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <File className=\"h-8 w-8 text-blue-600 flex-shrink-0\" />\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                        {uploadFile.file.name}\n                      </p>\n                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                        {formatFileSize(uploadFile.file.size)}\n                      </p>\n                      {uploadFile.status === 'uploading' && (\n                        <div className=\"w-full bg-gray-200 rounded-full h-2 mt-1\">\n                          <div \n                            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                            style={{ width: `${uploadFile.progress}%` }}\n                          />\n                        </div>\n                      )}\n                      {uploadFile.error && (\n                        <p className=\"text-xs text-red-600 mt-1\">{uploadFile.error}</p>\n                      )}\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      {uploadFile.status === 'completed' && (\n                        <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                      )}\n                      {uploadFile.status === 'error' && (\n                        <AlertCircle className=\"h-5 w-5 text-red-600\" />\n                      )}\n                      <button\n                        onClick={() => removeFile(uploadFile.id)}\n                        className=\"text-gray-400 hover:text-red-600\"\n                      >\n                        <X className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAiBe,SAAS,WAAW,EAAE,OAAO,EAAmB;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAElE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,MAAM,YAAY,MAAM,IAAI,CAAC;QAC7B,MAAM,cAA4B,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvD;gBACA,IAAI;gBACJ,UAAU;gBACV,QAAQ;YACV,CAAC;QACD,SAAS,CAAA,OAAQ;mBAAI;mBAAS;aAAY;IAC5C,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC7C;IAEA,MAAM,aAAa,OAAO;QACxB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,WAAW,EAAE,GAAG;oBAAE,GAAG,CAAC;oBAAE,QAAQ;gBAAY,IAAI;QAG3D,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;YACvC,SAAS,MAAM,CAAC,YAAY,SAAS,qBAAqB;;YAE1D,2BAA2B;YAC3B,MAAM,mBAAmB,YAAY;gBACnC,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;wBACxB,IAAI,EAAE,EAAE,KAAK,WAAW,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI;4BAC7C,OAAO;gCAAE,GAAG,CAAC;gCAAE,UAAU,EAAE,QAAQ,GAAG;4BAAG;wBAC3C;wBACA,OAAO;oBACT;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe;gBACjE;gBACA,MAAM;YACR;YAEA,cAAc;YAEd,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,WAAW,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,UAAU;4BAAK,QAAQ;wBAAY,IAAI;YAE5E,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,WAAW,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,QAAQ;4BAAS;wBAAM,IAAI;YAEhE;QACF,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,WAAW,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,QAAQ;wBAAS,OAAO;oBAAgB,IAAI;QAEjF;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,OAAO,CAAC;IACpD;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QACd,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE;YACxB,SAAS,EAAE,YAAY,CAAC,KAAK;QAC/B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCACjE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAW,CAAC,oEAAoE,EAC9E,aACI,mDACA,wCACJ;4BACF,YAAY;4BACZ,aAAa;4BACb,QAAQ;;8CAER,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAE,WAAU;8CAAyD;;;;;;8CAGtE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCACC,SAAS,IAAM,aAAa,OAAO,EAAE;oCACrC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAQ;oCACR,UAAU;oCACV,WAAU;;;;;;;;;;;;wBAKb,MAAM,MAAM,GAAG,mBACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAoD;gDACxD,MAAM,MAAM;gDAAC;;;;;;;sDAEvB,8OAAC;4CACC,SAAS;4CACT,UAAU,MAAM,KAAK,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;4CACxC,WAAU;sDACX;;;;;;;;;;;;8CAKH,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,2BACV,8OAAC;4CAAwB,WAAU;;8DACjC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,WAAW,IAAI,CAAC,IAAI;;;;;;sEAEvB,8OAAC;4DAAE,WAAU;sEACV,eAAe,WAAW,IAAI,CAAC,IAAI;;;;;;wDAErC,WAAW,MAAM,KAAK,6BACrB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,WAAW,QAAQ,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;wDAI/C,WAAW,KAAK,kBACf,8OAAC;4DAAE,WAAU;sEAA6B,WAAW,KAAK;;;;;;;;;;;;8DAG9D,8OAAC;oDAAI,WAAU;;wDACZ,WAAW,MAAM,KAAK,6BACrB,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAExB,WAAW,MAAM,KAAK,yBACrB,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEAEzB,8OAAC;4DACC,SAAS,IAAM,WAAW,WAAW,EAAE;4DACvC,WAAU;sEAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;;;;;;;2CAhCT,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CzC", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/components/NetworkScan.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Wifi, Users, Share2, Download, RefreshCw } from 'lucide-react'\n\ninterface NetworkScanProps {\n  onClose: () => void\n}\n\ninterface NearbyUser {\n  id: string\n  userId: string\n  deviceName: string\n  distance: string\n  filesShared: number\n  lastSeen: string\n  status: 'online' | 'away' | 'offline'\n}\n\nexport default function NetworkScan({ onClose }: NetworkScanProps) {\n  const [scanning, setScanning] = useState(false)\n  const [nearbyUsers, setNearbyUsers] = useState<NearbyUser[]>([])\n  const [scanComplete, setScanComplete] = useState(false)\n\n  // Mock data for demonstration\n  const mockUsers: NearbyUser[] = [\n    {\n      id: '1',\n      userId: '789012',\n      deviceName: 'John\\'s Laptop',\n      distance: '2m',\n      filesShared: 15,\n      lastSeen: 'Just now',\n      status: 'online'\n    },\n    {\n      id: '2',\n      userId: '345678',\n      deviceName: 'Sarah\\'s Phone',\n      distance: '5m',\n      filesShared: 8,\n      lastSeen: '2 min ago',\n      status: 'online'\n    },\n    {\n      id: '3',\n      userId: '901234',\n      deviceName: 'Office Desktop',\n      distance: '12m',\n      filesShared: 23,\n      lastSeen: '5 min ago',\n      status: 'away'\n    },\n    {\n      id: '4',\n      userId: '567890',\n      deviceName: 'Mike\\'s Tablet',\n      distance: '8m',\n      filesShared: 4,\n      lastSeen: '10 min ago',\n      status: 'away'\n    }\n  ]\n\n  const startScan = () => {\n    setScanning(true)\n    setScanComplete(false)\n    setNearbyUsers([])\n\n    // Simulate network scanning\n    setTimeout(() => {\n      // Add users one by one to simulate discovery\n      mockUsers.forEach((user, index) => {\n        setTimeout(() => {\n          setNearbyUsers(prev => [...prev, user])\n          if (index === mockUsers.length - 1) {\n            setScanning(false)\n            setScanComplete(true)\n          }\n        }, (index + 1) * 800)\n      })\n    }, 1000)\n  }\n\n  useEffect(() => {\n    // Auto-start scan when component mounts\n    startScan()\n  }, [])\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'online': return 'bg-green-500'\n      case 'away': return 'bg-yellow-500'\n      case 'offline': return 'bg-gray-500'\n      default: return 'bg-gray-500'\n    }\n  }\n\n  const handleConnectToUser = (user: NearbyUser) => {\n    alert(`Connecting to ${user.deviceName} (${user.userId})...`)\n    // Here you would implement WebRTC connection logic\n  }\n\n  const handleRequestFiles = (user: NearbyUser) => {\n    alert(`Requesting file list from ${user.deviceName}...`)\n    // Here you would implement file list request logic\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <div className=\"flex items-center space-x-3\">\n            <Wifi className=\"h-6 w-6 text-blue-600\" />\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Network Scan</h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Scan Status */}\n          <div className=\"text-center mb-6\">\n            {scanning ? (\n              <div>\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n                <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Scanning for nearby devices...\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Looking for Vadukondi users on your network\n                </p>\n              </div>\n            ) : scanComplete ? (\n              <div>\n                <Users className=\"h-12 w-12 text-green-600 mx-auto mb-4\" />\n                <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Scan Complete\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Found {nearbyUsers.length} nearby users\n                </p>\n              </div>\n            ) : (\n              <div>\n                <Wifi className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Ready to Scan\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Click scan to find nearby Vadukondi users\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* Scan Button */}\n          <div className=\"text-center mb-6\">\n            <button\n              onClick={startScan}\n              disabled={scanning}\n              className=\"flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg transition-colors mx-auto\"\n            >\n              <RefreshCw className={`h-5 w-5 ${scanning ? 'animate-spin' : ''}`} />\n              <span>{scanning ? 'Scanning...' : 'Scan Network'}</span>\n            </button>\n          </div>\n\n          {/* Nearby Users List */}\n          {nearbyUsers.length > 0 && (\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                Nearby Users ({nearbyUsers.length})\n              </h3>\n              <div className=\"space-y-3 max-h-60 overflow-y-auto\">\n                {nearbyUsers.map((user) => (\n                  <div key={user.id} className=\"flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <div className=\"relative\">\n                      <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold\">\n                        {user.userId.slice(0, 2)}\n                      </div>\n                      <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getStatusColor(user.status)}`}></div>\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {user.deviceName}\n                      </h4>\n                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                        ID: {user.userId} • {user.distance} away\n                      </p>\n                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                        {user.filesShared} files shared • {user.lastSeen}\n                      </p>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => handleRequestFiles(user)}\n                        className=\"flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors\"\n                      >\n                        <Download className=\"h-3 w-3\" />\n                        <span>Files</span>\n                      </button>\n                      <button\n                        onClick={() => handleConnectToUser(user)}\n                        className=\"flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors\"\n                      >\n                        <Share2 className=\"h-3 w-3\" />\n                        <span>Connect</span>\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Help Text */}\n          <div className=\"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n            <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n              <strong>How it works:</strong> Network scan uses WebRTC to discover other Vadukondi users \n              on your local network. Files are transferred directly between devices without going through our servers.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAmBe,SAAS,YAAY,EAAE,OAAO,EAAoB;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,8BAA8B;IAC9B,MAAM,YAA0B;QAC9B;YACE,IAAI;YACJ,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;QACV;QACA;YACE,IAAI;YACJ,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;QACV;QACA;YACE,IAAI;YACJ,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;QACV;QACA;YACE,IAAI;YACJ,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,YAAY;QAChB,YAAY;QACZ,gBAAgB;QAChB,eAAe,EAAE;QAEjB,4BAA4B;QAC5B,WAAW;YACT,6CAA6C;YAC7C,UAAU,OAAO,CAAC,CAAC,MAAM;gBACvB,WAAW;oBACT,eAAe,CAAA,OAAQ;+BAAI;4BAAM;yBAAK;oBACtC,IAAI,UAAU,UAAU,MAAM,GAAG,GAAG;wBAClC,YAAY;wBACZ,gBAAgB;oBAClB;gBACF,GAAG,CAAC,QAAQ,CAAC,IAAI;YACnB;QACF,GAAG;IACL;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,CAAC,cAAc,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC;IAC5D,mDAAmD;IACrD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,CAAC,0BAA0B,EAAE,KAAK,UAAU,CAAC,GAAG,CAAC;IACvD,mDAAmD;IACrD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;;;;;;;sCAEnE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,yBACC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;uCAIxD,6BACF,8OAAC;;kDACC,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,8OAAC;wCAAE,WAAU;;4CAA2C;4CAC/C,YAAY,MAAM;4CAAC;;;;;;;;;;;;qDAI9B,8OAAC;;kDACC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAQ9D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,iBAAiB,IAAI;;;;;;kDACjE,8OAAC;kDAAM,WAAW,gBAAgB;;;;;;;;;;;;;;;;;wBAKrC,YAAY,MAAM,GAAG,mBACpB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAAyD;wCACtD,YAAY,MAAM;wCAAC;;;;;;;8CAEpC,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG;;;;;;sEAExB,8OAAC;4DAAI,WAAW,CAAC,uEAAuE,EAAE,eAAe,KAAK,MAAM,GAAG;;;;;;;;;;;;8DAEzH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,UAAU;;;;;;sEAElB,8OAAC;4DAAE,WAAU;;gEAA2C;gEACjD,KAAK,MAAM;gEAAC;gEAAI,KAAK,QAAQ;gEAAC;;;;;;;sEAErC,8OAAC;4DAAE,WAAU;;gEACV,KAAK,WAAW;gEAAC;gEAAiB,KAAK,QAAQ;;;;;;;;;;;;;8DAGpD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,mBAAmB;4DAClC,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DACC,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;2CA/BF,KAAK,EAAE;;;;;;;;;;;;;;;;sCAyCzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;kDAAO;;;;;;oCAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/components/PublicFiles.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Download, Search, Filter, File, Image, Video, Music, Archive } from 'lucide-react'\n\ninterface PublicFilesProps {\n  onClose: () => void\n}\n\ninterface PublicFile {\n  id: string\n  name: string\n  size: number\n  type: string\n  uploaderId: string\n  uploadedAt: string\n  downloads: number\n  category: string\n}\n\nexport default function PublicFiles({ onClose }: PublicFilesProps) {\n  const [files, setFiles] = useState<PublicFile[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('all')\n\n  // Mock data for demonstration\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setFiles([\n        {\n          id: '1',\n          name: 'Sample_Document.pdf',\n          size: 2048576,\n          type: 'application/pdf',\n          uploaderId: '123456',\n          uploadedAt: '2024-01-15T10:30:00Z',\n          downloads: 45,\n          category: 'document'\n        },\n        {\n          id: '2',\n          name: 'Nature_Photo.jpg',\n          size: 5242880,\n          type: 'image/jpeg',\n          uploaderId: '789012',\n          uploadedAt: '2024-01-14T15:20:00Z',\n          downloads: 128,\n          category: 'image'\n        },\n        {\n          id: '3',\n          name: 'Music_Track.mp3',\n          size: 8388608,\n          type: 'audio/mpeg',\n          uploaderId: '345678',\n          uploadedAt: '2024-01-13T09:45:00Z',\n          downloads: 67,\n          category: 'audio'\n        },\n        {\n          id: '4',\n          name: 'Project_Archive.zip',\n          size: 15728640,\n          type: 'application/zip',\n          uploaderId: '901234',\n          uploadedAt: '2024-01-12T14:10:00Z',\n          downloads: 23,\n          category: 'archive'\n        },\n        {\n          id: '5',\n          name: 'Tutorial_Video.mp4',\n          size: 52428800,\n          type: 'video/mp4',\n          uploaderId: '567890',\n          uploadedAt: '2024-01-11T11:30:00Z',\n          downloads: 89,\n          category: 'video'\n        }\n      ])\n      setLoading(false)\n    }, 1000)\n  }, [])\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString()\n  }\n\n  const getFileIcon = (type: string, category: string) => {\n    if (category === 'image') return <Image className=\"h-6 w-6 text-green-600\" />\n    if (category === 'video') return <Video className=\"h-6 w-6 text-red-600\" />\n    if (category === 'audio') return <Music className=\"h-6 w-6 text-purple-600\" />\n    if (category === 'archive') return <Archive className=\"h-6 w-6 text-orange-600\" />\n    return <File className=\"h-6 w-6 text-blue-600\" />\n  }\n\n  const handleDownload = async (file: PublicFile) => {\n    try {\n      // Simulate download\n      const response = await fetch(`/api/files/download/${file.id}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`\n        }\n      })\n      \n      if (response.ok) {\n        // Create download link\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = file.name\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n        \n        // Update download count\n        setFiles(prev => prev.map(f => \n          f.id === file.id ? { ...f, downloads: f.downloads + 1 } : f\n        ))\n      }\n    } catch (error) {\n      console.error('Download failed:', error)\n      alert('Download failed. Please try again.')\n    }\n  }\n\n  const filteredFiles = files.filter(file => {\n    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesCategory = selectedCategory === 'all' || file.category === selectedCategory\n    return matchesSearch && matchesCategory\n  })\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Public Files</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"p-6 border-b bg-gray-50 dark:bg-gray-700\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search files...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white\"\n              />\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-5 w-5 text-gray-400\" />\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white\"\n              >\n                <option value=\"all\">All Categories</option>\n                <option value=\"document\">Documents</option>\n                <option value=\"image\">Images</option>\n                <option value=\"video\">Videos</option>\n                <option value=\"audio\">Audio</option>\n                <option value=\"archive\">Archives</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* File List */}\n        <div className=\"flex-1 overflow-y-auto p-6\">\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n              <p className=\"mt-4 text-gray-600 dark:text-gray-400\">Loading public files...</p>\n            </div>\n          ) : filteredFiles.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <File className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 dark:text-gray-400\">No files found</p>\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {filteredFiles.map((file) => (\n                <div key={file.id} className=\"flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\">\n                  {getFileIcon(file.type, file.category)}\n                  <div className=\"flex-1 min-w-0\">\n                    <h3 className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {file.name}\n                    </h3>\n                    <div className=\"flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-400\">\n                      <span>{formatFileSize(file.size)}</span>\n                      <span>By: {file.uploaderId}</span>\n                      <span>{formatDate(file.uploadedAt)}</span>\n                      <span>{file.downloads} downloads</span>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => handleDownload(file)}\n                    className=\"flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\"\n                  >\n                    <Download className=\"h-4 w-4\" />\n                    <span>Download</span>\n                  </button>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAoBe,SAAS,YAAY,EAAE,OAAO,EAAoB;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;QACpB,WAAW;YACT,SAAS;gBACP;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,YAAY;oBACZ,YAAY;oBACZ,WAAW;oBACX,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,YAAY;oBACZ,YAAY;oBACZ,WAAW;oBACX,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,YAAY;oBACZ,YAAY;oBACZ,WAAW;oBACX,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,YAAY;oBACZ,YAAY;oBACZ,WAAW;oBACX,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,YAAY;oBACZ,YAAY;oBACZ,WAAW;oBACX,UAAU;gBACZ;aACD;YACD,WAAW;QACb,GAAG;IACL,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,MAAM,cAAc,CAAC,MAAc;QACjC,IAAI,aAAa,SAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAClD,IAAI,aAAa,SAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAClD,IAAI,aAAa,SAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAClD,IAAI,aAAa,WAAW,qBAAO,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACtD,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,oBAAoB;YACpB,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,KAAK,EAAE,EAAE,EAAE;gBAC7D,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe;gBACjE;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,uBAAuB;gBACvB,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,KAAK,IAAI;gBACtB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,wBAAwB;gBACxB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,WAAW,EAAE,SAAS,GAAG;wBAAE,IAAI;YAE9D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC7E,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACxE,OAAO,iBAAiB;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCACjE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOhC,8OAAC;oBAAI,WAAU;8BACZ,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;+BAErD,cAAc,MAAM,KAAK,kBAC3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;6CAGlD,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gCAAkB,WAAU;;oCAC1B,YAAY,KAAK,IAAI,EAAE,KAAK,QAAQ;kDACrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAM,eAAe,KAAK,IAAI;;;;;;kEAC/B,8OAAC;;4DAAK;4DAAK,KAAK,UAAU;;;;;;;kEAC1B,8OAAC;kEAAM,WAAW,KAAK,UAAU;;;;;;kEACjC,8OAAC;;4DAAM,KAAK,SAAS;4DAAC;;;;;;;;;;;;;;;;;;;kDAG1B,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;;+BAlBA,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BjC", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { Share2, Upload, Users, Search, Settings, LogOut } from 'lucide-react'\nimport FileUpload from '@/components/FileUpload'\nimport NetworkScan from '@/components/NetworkScan'\nimport PublicFiles from '@/components/PublicFiles'\n\nexport default function Dashboard() {\n  const [userId, setUserId] = useState<string | null>(null)\n  const [mounted, setMounted] = useState(false)\n  const [showFileUpload, setShowFileUpload] = useState(false)\n  const [showNetworkScan, setShowNetworkScan] = useState(false)\n  const [showPublicFiles, setShowPublicFiles] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n    // Check if user is authenticated\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('auth-token')\n      if (!token) {\n        window.location.href = '/'\n        return\n      }\n      \n      // TODO: Verify token and get user info\n      // For now, we'll extract userId from token or set a placeholder\n      setUserId('123456') // Placeholder\n    }\n  }, [])\n\n  const handleLogout = () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth-token')\n      window.location.href = '/'\n    }\n  }\n\n  if (!mounted) {\n    return null\n  }\n\n  if (!userId) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Share2 className=\"h-8 w-8 text-blue-600\" />\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Vadukondi</h1>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">ID: {userId}</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white\">\n                <Settings className=\"h-5 w-5\" />\n              </button>\n              <button \n                onClick={handleLogout}\n                className=\"p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white\"\n              >\n                <LogOut className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Quick Actions */}\n        <div className=\"grid md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <Upload className=\"h-8 w-8 text-blue-600\" />\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">Upload Files</h2>\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              Drag and drop files or click to upload. Share instantly with anyone.\n            </p>\n            <button\n              onClick={() => setShowFileUpload(true)}\n              className=\"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              Choose Files\n            </button>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <Users className=\"h-8 w-8 text-green-600\" />\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">Nearby Users</h2>\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              Discover and share files with users on your local network.\n            </p>\n            <button\n              onClick={() => setShowNetworkScan(true)}\n              className=\"w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              Scan Network\n            </button>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <Search className=\"h-8 w-8 text-purple-600\" />\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">Public Files</h2>\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              Browse and download files shared publicly by the community.\n            </p>\n            <button\n              onClick={() => setShowPublicFiles(true)}\n              className=\"w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              Browse Public\n            </button>\n          </div>\n        </div>\n\n        {/* File Management Sections */}\n        <div className=\"grid lg:grid-cols-2 gap-8\">\n          {/* My Files */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border\">\n            <div className=\"p-6 border-b\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">My Files</h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Files you've uploaded</p>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"text-center py-8\">\n                <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-600 dark:text-gray-400\">No files uploaded yet</p>\n                <p className=\"text-sm text-gray-500 dark:text-gray-500\">Upload your first file to get started</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Recent Activity */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border\">\n            <div className=\"p-6 border-b\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Recent Activity</h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Your sharing history</p>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"text-center py-8\">\n                <Share2 className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-600 dark:text-gray-400\">No activity yet</p>\n                <p className=\"text-sm text-gray-500 dark:text-gray-500\">Start sharing files to see your activity</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"container mx-auto px-4 py-6 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"text-center\">\n          <p className=\"text-sm text-gray-500 dark:text-gray-500\">\n            Created by <span className=\"font-semibold text-blue-600 dark:text-blue-400\">Shaik Mahammad Yaseen</span> •\n            Vadukondi v1.0\n          </p>\n        </div>\n      </footer>\n\n      {/* Modals */}\n      {mounted && showFileUpload && (\n        <FileUpload onClose={() => setShowFileUpload(false)} />\n      )}\n      {mounted && showNetworkScan && (\n        <NetworkScan onClose={() => setShowNetworkScan(false)} />\n      )}\n      {mounted && showPublicFiles && (\n        <PublicFiles onClose={() => setShowPublicFiles(false)} />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,iCAAiC;QACjC,uCAAmC;;QAUnC;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,uCAAmC;;QAGnC;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;;oDAA2C;oDAAK;;;;;;;;;;;;;;;;;;;0CAGjE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;;;;;;;kDAEtE,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;;;;;;;kDAEtE,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;;;;;;;kDAEtE,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAE1D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;0CAM9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAE1D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAA2C;0CAC3C,8OAAC;gCAAK,WAAU;0CAAiD;;;;;;4BAA4B;;;;;;;;;;;;;;;;;YAO7G,WAAW,gCACV,8OAAC,gIAAA,CAAA,UAAU;gBAAC,SAAS,IAAM,kBAAkB;;;;;;YAE9C,WAAW,iCACV,8OAAC,iIAAA,CAAA,UAAW;gBAAC,SAAS,IAAM,mBAAmB;;;;;;YAEhD,WAAW,iCACV,8OAAC,iIAAA,CAAA,UAAW;gBAAC,SAAS,IAAM,mBAAmB;;;;;;;;;;;;AAIvD", "debugId": null}}]}