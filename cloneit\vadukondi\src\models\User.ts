import mongoose, { Schema, Document } from 'mongoose'
import { User } from '@/types'

export interface UserDocument extends User, Document {}

const UserSchema = new Schema<UserDocument>({
  id: {
    type: String,
    required: true,
    unique: true,
    match: /^\d{6}$/,
    index: true
  },
  passwordHash: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastActive: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
})

// Ensure the 6-digit ID is unique
UserSchema.index({ id: 1 }, { unique: true })

export default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema)
