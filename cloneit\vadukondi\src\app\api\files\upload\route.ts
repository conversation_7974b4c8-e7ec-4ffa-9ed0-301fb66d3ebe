import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth'
import { generateFileId } from '@/lib/utils'
import connectDB from '@/lib/database'
import File from '@/models/File'
import memoryStore from '@/lib/memory-store'

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const decoded = verifyToken(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const isPublic = formData.get('isPublic') === 'true'

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      )
    }

    if (file.size > 100 * 1024 * 1024) { // 100MB limit
      return NextResponse.json(
        { success: false, error: 'File is too large. Maximum size is 100MB.' },
        { status: 400 }
      )
    }

    // Read file data
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Determine category based on file type
    let category = 'other'
    if (file.type.startsWith('image/')) category = 'image'
    else if (file.type.startsWith('video/')) category = 'video'
    else if (file.type.startsWith('audio/')) category = 'audio'
    else if (file.type.includes('pdf') || file.type.includes('document')) category = 'document'
    else if (file.type.includes('zip') || file.type.includes('archive')) category = 'archive'

    const fileData = {
      id: generateFileId(),
      name: file.name,
      size: file.size,
      type: file.type,
      uploaderId: decoded.userId,
      uploadedAt: new Date(),
      downloads: 0,
      category,
      isPublic,
      data: buffer
    }

    try {
      // Try to save to MongoDB
      await connectDB()
      const dbFile = new File(fileData)
      await dbFile.save()
    } catch (dbError) {
      console.warn('MongoDB not available, using memory store:', dbError)
      // Fallback to memory store
      await memoryStore.createFile(fileData)
    }

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      file: {
        id: fileData.id,
        name: fileData.name,
        size: fileData.size,
        type: fileData.type,
        uploadedAt: fileData.uploadedAt.toISOString(),
        isPublic: fileData.isPublic
      }
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { success: false, error: 'Upload failed' },
      { status: 500 }
    )
  }
}
