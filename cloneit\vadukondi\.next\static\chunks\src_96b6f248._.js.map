{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/components/FileUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useCallback } from 'react'\nimport { Upload, X, File, CheckCircle, AlertCircle } from 'lucide-react'\n\ninterface FileUploadProps {\n  onClose: () => void\n}\n\ninterface UploadFile {\n  file: File\n  id: string\n  progress: number\n  status: 'pending' | 'uploading' | 'completed' | 'error'\n  error?: string\n}\n\nexport default function FileUpload({ onClose }: FileUploadProps) {\n  const [files, setFiles] = useState<UploadFile[]>([])\n  const [isDragOver, setIsDragOver] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const generateFileId = () => Math.random().toString(36).substr(2, 9)\n\n  const addFiles = useCallback((newFiles: FileList | File[]) => {\n    const fileArray = Array.from(newFiles)\n    const uploadFiles: UploadFile[] = fileArray.map(file => ({\n      file,\n      id: generateFileId(),\n      progress: 0,\n      status: 'pending'\n    }))\n    setFiles(prev => [...prev, ...uploadFiles])\n  }, [])\n\n  const removeFile = (id: string) => {\n    setFiles(prev => prev.filter(f => f.id !== id))\n  }\n\n  const uploadFile = async (uploadFile: UploadFile) => {\n    setFiles(prev => prev.map(f => \n      f.id === uploadFile.id ? { ...f, status: 'uploading' } : f\n    ))\n\n    try {\n      const formData = new FormData()\n      formData.append('file', uploadFile.file)\n      formData.append('isPublic', 'false') // Default to private\n\n      // Simulate upload progress\n      const progressInterval = setInterval(() => {\n        setFiles(prev => prev.map(f => {\n          if (f.id === uploadFile.id && f.progress < 90) {\n            return { ...f, progress: f.progress + 10 }\n          }\n          return f\n        }))\n      }, 200)\n\n      const response = await fetch('/api/files/upload', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`\n        },\n        body: formData\n      })\n\n      clearInterval(progressInterval)\n\n      if (response.ok) {\n        setFiles(prev => prev.map(f => \n          f.id === uploadFile.id ? { ...f, progress: 100, status: 'completed' } : f\n        ))\n      } else {\n        const error = await response.text()\n        setFiles(prev => prev.map(f => \n          f.id === uploadFile.id ? { ...f, status: 'error', error } : f\n        ))\n      }\n    } catch (error) {\n      setFiles(prev => prev.map(f => \n        f.id === uploadFile.id ? { ...f, status: 'error', error: 'Upload failed' } : f\n      ))\n    }\n  }\n\n  const uploadAllFiles = () => {\n    files.filter(f => f.status === 'pending').forEach(uploadFile)\n  }\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(true)\n  }\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(false)\n  }\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(false)\n    if (e.dataTransfer.files) {\n      addFiles(e.dataTransfer.files)\n    }\n  }\n\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files) {\n      addFiles(e.target.files)\n    }\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Upload Files</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Drop Zone */}\n          <div\n            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n              isDragOver \n                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' \n                : 'border-gray-300 dark:border-gray-600'\n            }`}\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n          >\n            <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              Drop files here or click to browse\n            </p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n              Support for any file type, unlimited size\n            </p>\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors\"\n            >\n              Choose Files\n            </button>\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              multiple\n              onChange={handleFileSelect}\n              className=\"hidden\"\n            />\n          </div>\n\n          {/* File List */}\n          {files.length > 0 && (\n            <div className=\"mt-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Files ({files.length})\n                </h3>\n                <button\n                  onClick={uploadAllFiles}\n                  disabled={files.every(f => f.status !== 'pending')}\n                  className=\"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors\"\n                >\n                  Upload All\n                </button>\n              </div>\n\n              <div className=\"max-h-60 overflow-y-auto space-y-2\">\n                {files.map((uploadFile) => (\n                  <div key={uploadFile.id} className=\"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <File className=\"h-8 w-8 text-blue-600 flex-shrink-0\" />\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                        {uploadFile.file.name}\n                      </p>\n                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                        {formatFileSize(uploadFile.file.size)}\n                      </p>\n                      {uploadFile.status === 'uploading' && (\n                        <div className=\"w-full bg-gray-200 rounded-full h-2 mt-1\">\n                          <div \n                            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                            style={{ width: `${uploadFile.progress}%` }}\n                          />\n                        </div>\n                      )}\n                      {uploadFile.error && (\n                        <p className=\"text-xs text-red-600 mt-1\">{uploadFile.error}</p>\n                      )}\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      {uploadFile.status === 'completed' && (\n                        <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                      )}\n                      {uploadFile.status === 'error' && (\n                        <AlertCircle className=\"h-5 w-5 text-red-600\" />\n                      )}\n                      <button\n                        onClick={() => removeFile(uploadFile.id)}\n                        className=\"text-gray-400 hover:text-red-600\"\n                      >\n                        <X className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAiBe,SAAS,WAAW,EAAE,OAAO,EAAmB;;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAElE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,CAAC;YAC5B,MAAM,YAAY,MAAM,IAAI,CAAC;YAC7B,MAAM,cAA4B,UAAU,GAAG;gEAAC,CAAA,OAAQ,CAAC;wBACvD;wBACA,IAAI;wBACJ,UAAU;wBACV,QAAQ;oBACV,CAAC;;YACD;oDAAS,CAAA,OAAQ;2BAAI;2BAAS;qBAAY;;QAC5C;2CAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC7C;IAEA,MAAM,aAAa,OAAO;QACxB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,WAAW,EAAE,GAAG;oBAAE,GAAG,CAAC;oBAAE,QAAQ;gBAAY,IAAI;QAG3D,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;YACvC,SAAS,MAAM,CAAC,YAAY,SAAS,qBAAqB;;YAE1D,2BAA2B;YAC3B,MAAM,mBAAmB,YAAY;gBACnC,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;wBACxB,IAAI,EAAE,EAAE,KAAK,WAAW,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI;4BAC7C,OAAO;gCAAE,GAAG,CAAC;gCAAE,UAAU,EAAE,QAAQ,GAAG;4BAAG;wBAC3C;wBACA,OAAO;oBACT;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe;gBACjE;gBACA,MAAM;YACR;YAEA,cAAc;YAEd,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,WAAW,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,UAAU;4BAAK,QAAQ;wBAAY,IAAI;YAE5E,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,WAAW,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,QAAQ;4BAAS;wBAAM,IAAI;YAEhE;QACF,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,WAAW,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,QAAQ;wBAAS,OAAO;oBAAgB,IAAI;QAEjF;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,OAAO,CAAC;IACpD;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QACd,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE;YACxB,SAAS,EAAE,YAAY,CAAC,KAAK;QAC/B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmD;;;;;;sCACjE,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,WAAW,CAAC,oEAAoE,EAC9E,aACI,mDACA,wCACJ;4BACF,YAAY;4BACZ,aAAa;4BACb,QAAQ;;8CAER,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAE,WAAU;8CAAyD;;;;;;8CAGtE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCACC,SAAS,IAAM,aAAa,OAAO,EAAE;oCACrC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAQ;oCACR,UAAU;oCACV,WAAU;;;;;;;;;;;;wBAKb,MAAM,MAAM,GAAG,mBACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAoD;gDACxD,MAAM,MAAM;gDAAC;;;;;;;sDAEvB,6LAAC;4CACC,SAAS;4CACT,UAAU,MAAM,KAAK,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;4CACxC,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,2BACV,6LAAC;4CAAwB,WAAU;;8DACjC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,WAAW,IAAI,CAAC,IAAI;;;;;;sEAEvB,6LAAC;4DAAE,WAAU;sEACV,eAAe,WAAW,IAAI,CAAC,IAAI;;;;;;wDAErC,WAAW,MAAM,KAAK,6BACrB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,WAAW,QAAQ,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;wDAI/C,WAAW,KAAK,kBACf,6LAAC;4DAAE,WAAU;sEAA6B,WAAW,KAAK;;;;;;;;;;;;8DAG9D,6LAAC;oDAAI,WAAU;;wDACZ,WAAW,MAAM,KAAK,6BACrB,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAExB,WAAW,MAAM,KAAK,yBACrB,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEAEzB,6LAAC;4DACC,SAAS,IAAM,WAAW,WAAW,EAAE;4DACvC,WAAU;sEAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;;;;;;;2CAhCT,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CzC;GAtNwB;KAAA", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/components/NetworkScan.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Wifi, Users, Share2, Download, RefreshCw, Bluetooth } from 'lucide-react'\n\ninterface NetworkScanProps {\n  onClose: () => void\n}\n\ninterface NearbyDevice {\n  id: string\n  name: string\n  type: 'bluetooth' | 'network'\n  connected: boolean\n  distance?: string\n}\n\nexport default function NetworkScan({ onClose }: NetworkScanProps) {\n  const [scanning, setScanning] = useState(false)\n  const [nearbyDevices, setNearbyDevices] = useState<NearbyDevice[]>([])\n  const [scanComplete, setScanComplete] = useState(false)\n  const [bluetoothSupported, setBluetoothSupported] = useState(false)\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    // Check if Bluetooth is supported\n    if (typeof navigator !== 'undefined' && 'bluetooth' in navigator) {\n      setBluetoothSupported(true)\n    }\n  }, [])\n\n  const startBluetoothScan = async () => {\n    if (!bluetoothSupported) {\n      setError('Bluetooth is not supported in this browser')\n      return\n    }\n\n    setScanning(true)\n    setScanComplete(false)\n    setNearbyDevices([])\n    setError('')\n\n    try {\n      // Request Bluetooth device\n      const device = await navigator.bluetooth.requestDevice({\n        acceptAllDevices: true,\n        optionalServices: ['battery_service', 'device_information']\n      })\n\n      if (device) {\n        const newDevice: NearbyDevice = {\n          id: device.id || `bt-${Date.now()}`,\n          name: device.name || 'Unknown Device',\n          type: 'bluetooth',\n          connected: device.gatt?.connected || false\n        }\n\n        setNearbyDevices([newDevice])\n        setScanComplete(true)\n      }\n    } catch (err: any) {\n      if (err.name === 'NotFoundError') {\n        setError('No Bluetooth devices found or user cancelled')\n      } else if (err.name === 'NotAllowedError') {\n        setError('Bluetooth access denied. Please allow Bluetooth permissions.')\n      } else {\n        setError(`Bluetooth error: ${err.message}`)\n      }\n    } finally {\n      setScanning(false)\n    }\n  }\n\n  const startNetworkScan = async () => {\n    setScanning(true)\n    setScanComplete(false)\n    setNearbyDevices([])\n    setError('')\n\n    // Real network scanning would require WebRTC or server-side discovery\n    // For now, we'll show a message about network discovery\n    setTimeout(() => {\n      setError('Network discovery requires WebRTC peer connection setup')\n      setScanning(false)\n      setScanComplete(true)\n    }, 2000)\n  }\n\n  const connectToDevice = async (device: NearbyDevice) => {\n    if (device.type === 'bluetooth') {\n      try {\n        const bluetoothDevice = await navigator.bluetooth.requestDevice({\n          filters: [{ name: device.name }]\n        })\n\n        if (bluetoothDevice.gatt) {\n          const server = await bluetoothDevice.gatt.connect()\n          alert(`Connected to ${device.name}!`)\n\n          // Update device status\n          setNearbyDevices(prev =>\n            prev.map(d =>\n              d.id === device.id ? { ...d, connected: true } : d\n            )\n          )\n        }\n      } catch (err: any) {\n        alert(`Failed to connect: ${err.message}`)\n      }\n    } else {\n      alert('Network device connection not implemented yet')\n    }\n  }\n\n  const shareFiles = (device: NearbyDevice) => {\n    alert(`File sharing with ${device.name} would be implemented here`)\n    // This would implement actual file transfer via Bluetooth or WebRTC\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <div className=\"flex items-center space-x-3\">\n            <Wifi className=\"h-6 w-6 text-blue-600\" />\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Network Scan</h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Error Display */}\n          {error && (\n            <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded mb-4\">\n              {error}\n            </div>\n          )}\n\n          {/* Scan Status */}\n          <div className=\"text-center mb-6\">\n            {scanning ? (\n              <div>\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n                <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Scanning for devices...\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Looking for nearby Bluetooth and network devices\n                </p>\n              </div>\n            ) : scanComplete ? (\n              <div>\n                <Users className=\"h-12 w-12 text-green-600 mx-auto mb-4\" />\n                <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Scan Complete\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Found {nearbyDevices.length} nearby devices\n                </p>\n              </div>\n            ) : (\n              <div>\n                <Wifi className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Device Discovery\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Scan for Bluetooth devices or network peers\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* Scan Buttons */}\n          <div className=\"flex gap-4 justify-center mb-6\">\n            <button\n              onClick={startBluetoothScan}\n              disabled={scanning || !bluetoothSupported}\n              className=\"flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors\"\n            >\n              <Bluetooth className={`h-5 w-5 ${scanning ? 'animate-spin' : ''}`} />\n              <span>Bluetooth</span>\n            </button>\n            <button\n              onClick={startNetworkScan}\n              disabled={scanning}\n              className=\"flex items-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors\"\n            >\n              <Wifi className={`h-5 w-5 ${scanning ? 'animate-spin' : ''}`} />\n              <span>Network</span>\n            </button>\n          </div>\n\n          {!bluetoothSupported && (\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-yellow-700 dark:text-yellow-400 px-4 py-3 rounded mb-4\">\n              Bluetooth is not supported in this browser. Try Chrome or Edge for Bluetooth functionality.\n            </div>\n          )}\n\n          {/* Nearby Devices List */}\n          {nearbyDevices.length > 0 && (\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                Discovered Devices ({nearbyDevices.length})\n              </h3>\n              <div className=\"space-y-3 max-h-60 overflow-y-auto\">\n                {nearbyDevices.map((device) => (\n                  <div key={device.id} className=\"flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <div className=\"relative\">\n                      <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white\">\n                        {device.type === 'bluetooth' ? (\n                          <Bluetooth className=\"h-6 w-6\" />\n                        ) : (\n                          <Wifi className=\"h-6 w-6\" />\n                        )}\n                      </div>\n                      <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${\n                        device.connected ? 'bg-green-500' : 'bg-gray-500'\n                      }`}></div>\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {device.name}\n                      </h4>\n                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                        {device.type === 'bluetooth' ? 'Bluetooth Device' : 'Network Device'}\n                        {device.distance && ` • ${device.distance} away`}\n                      </p>\n                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                        Status: {device.connected ? 'Connected' : 'Available'}\n                      </p>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => shareFiles(device)}\n                        className=\"flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors\"\n                      >\n                        <Share2 className=\"h-3 w-3\" />\n                        <span>Share</span>\n                      </button>\n                      <button\n                        onClick={() => connectToDevice(device)}\n                        disabled={device.connected}\n                        className=\"flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-xs transition-colors\"\n                      >\n                        <Download className=\"h-3 w-3\" />\n                        <span>{device.connected ? 'Connected' : 'Connect'}</span>\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Help Text */}\n          <div className=\"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n            <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n              <strong>Real Device Discovery:</strong> Bluetooth scanning uses the Web Bluetooth API to find actual nearby devices.\n              Network discovery would require WebRTC peer connections. Files can be transferred directly between connected devices.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAiBe,SAAS,YAAY,EAAE,OAAO,EAAoB;;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,kCAAkC;YAClC,IAAI,OAAO,cAAc,eAAe,eAAe,WAAW;gBAChE,sBAAsB;YACxB;QACF;gCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,oBAAoB;YACvB,SAAS;YACT;QACF;QAEA,YAAY;QACZ,gBAAgB;QAChB,iBAAiB,EAAE;QACnB,SAAS;QAET,IAAI;YACF,2BAA2B;YAC3B,MAAM,SAAS,MAAM,UAAU,SAAS,CAAC,aAAa,CAAC;gBACrD,kBAAkB;gBAClB,kBAAkB;oBAAC;oBAAmB;iBAAqB;YAC7D;YAEA,IAAI,QAAQ;gBACV,MAAM,YAA0B;oBAC9B,IAAI,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI;oBACnC,MAAM,OAAO,IAAI,IAAI;oBACrB,MAAM;oBACN,WAAW,OAAO,IAAI,EAAE,aAAa;gBACvC;gBAEA,iBAAiB;oBAAC;iBAAU;gBAC5B,gBAAgB;YAClB;QACF,EAAE,OAAO,KAAU;YACjB,IAAI,IAAI,IAAI,KAAK,iBAAiB;gBAChC,SAAS;YACX,OAAO,IAAI,IAAI,IAAI,KAAK,mBAAmB;gBACzC,SAAS;YACX,OAAO;gBACL,SAAS,CAAC,iBAAiB,EAAE,IAAI,OAAO,EAAE;YAC5C;QACF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAY;QACZ,gBAAgB;QAChB,iBAAiB,EAAE;QACnB,SAAS;QAET,sEAAsE;QACtE,wDAAwD;QACxD,WAAW;YACT,SAAS;YACT,YAAY;YACZ,gBAAgB;QAClB,GAAG;IACL;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,OAAO,IAAI,KAAK,aAAa;YAC/B,IAAI;gBACF,MAAM,kBAAkB,MAAM,UAAU,SAAS,CAAC,aAAa,CAAC;oBAC9D,SAAS;wBAAC;4BAAE,MAAM,OAAO,IAAI;wBAAC;qBAAE;gBAClC;gBAEA,IAAI,gBAAgB,IAAI,EAAE;oBACxB,MAAM,SAAS,MAAM,gBAAgB,IAAI,CAAC,OAAO;oBACjD,MAAM,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC;oBAEpC,uBAAuB;oBACvB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,OAAO,EAAE,GAAG;gCAAE,GAAG,CAAC;gCAAE,WAAW;4BAAK,IAAI;gBAGvD;YACF,EAAE,OAAO,KAAU;gBACjB,MAAM,CAAC,mBAAmB,EAAE,IAAI,OAAO,EAAE;YAC3C;QACF,OAAO;YACL,MAAM;QACR;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,CAAC,kBAAkB,EAAE,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAClE,oEAAoE;IACtE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAG,WAAU;8CAAmD;;;;;;;;;;;;sCAEnE,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;;wBAEZ,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAKL,6LAAC;4BAAI,WAAU;sCACZ,yBACC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;uCAIxD,6BACF,6LAAC;;kDACC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,6LAAC;wCAAE,WAAU;;4CAA2C;4CAC/C,cAAc,MAAM;4CAAC;;;;;;;;;;;;qDAIhC,6LAAC;;kDACC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAQ9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU,YAAY,CAAC;oCACvB,WAAU;;sDAEV,6LAAC,+MAAA,CAAA,YAAS;4CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,iBAAiB,IAAI;;;;;;sDACjE,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,iBAAiB,IAAI;;;;;;sDAC5D,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;wBAIT,CAAC,oCACA,6LAAC;4BAAI,WAAU;sCAAiJ;;;;;;wBAMjK,cAAc,MAAM,GAAG,mBACtB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAyD;wCAChD,cAAc,MAAM;wCAAC;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,OAAO,IAAI,KAAK,4BACf,6LAAC,+MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;qFAErB,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAGpB,6LAAC;4DAAI,WAAW,CAAC,uEAAuE,EACtF,OAAO,SAAS,GAAG,iBAAiB,eACpC;;;;;;;;;;;;8DAEJ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,OAAO,IAAI;;;;;;sEAEd,6LAAC;4DAAE,WAAU;;gEACV,OAAO,IAAI,KAAK,cAAc,qBAAqB;gEACnD,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC;;;;;;;sEAElD,6LAAC;4DAAE,WAAU;;gEAA2C;gEAC7C,OAAO,SAAS,GAAG,cAAc;;;;;;;;;;;;;8DAG9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,IAAM,WAAW;4DAC1B,WAAU;;8EAEV,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DACC,SAAS,IAAM,gBAAgB;4DAC/B,UAAU,OAAO,SAAS;4DAC1B,WAAU;;8EAEV,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;8EAAM,OAAO,SAAS,GAAG,cAAc;;;;;;;;;;;;;;;;;;;2CAvCpC,OAAO,EAAE;;;;;;;;;;;;;;;;sCAiD3B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;kDAAO;;;;;;oCAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GA9PwB;KAAA", "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/components/PublicFiles.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Download, Search, Filter, File, Image, Video, Music, Archive } from 'lucide-react'\n\ninterface PublicFilesProps {\n  onClose: () => void\n}\n\ninterface PublicFile {\n  id: string\n  name: string\n  size: number\n  type: string\n  uploaderId: string\n  uploadedAt: string\n  downloads: number\n  category: string\n}\n\nexport default function PublicFiles({ onClose }: PublicFilesProps) {\n  const [files, setFiles] = useState<PublicFile[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('all')\n\n  // Mock data for demonstration\n  useEffect(() => {\n    fetchPublicFiles()\n  }, [])\n\n  const fetchPublicFiles = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/files/public', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`\n        }\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setFiles(data.files || [])\n      } else {\n        console.error('Failed to fetch public files')\n        setFiles([])\n      }\n    } catch (error) {\n      console.error('Error fetching public files:', error)\n      setFiles([])\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString()\n  }\n\n  const getFileIcon = (type: string, category: string) => {\n    if (category === 'image') return <Image className=\"h-6 w-6 text-green-600\" />\n    if (category === 'video') return <Video className=\"h-6 w-6 text-red-600\" />\n    if (category === 'audio') return <Music className=\"h-6 w-6 text-purple-600\" />\n    if (category === 'archive') return <Archive className=\"h-6 w-6 text-orange-600\" />\n    return <File className=\"h-6 w-6 text-blue-600\" />\n  }\n\n  const handleDownload = async (file: PublicFile) => {\n    try {\n      // Simulate download\n      const response = await fetch(`/api/files/download/${file.id}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`\n        }\n      })\n      \n      if (response.ok) {\n        // Create download link\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = file.name\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n        \n        // Update download count\n        setFiles(prev => prev.map(f => \n          f.id === file.id ? { ...f, downloads: f.downloads + 1 } : f\n        ))\n      }\n    } catch (error) {\n      console.error('Download failed:', error)\n      alert('Download failed. Please try again.')\n    }\n  }\n\n  const filteredFiles = files.filter(file => {\n    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesCategory = selectedCategory === 'all' || file.category === selectedCategory\n    return matchesSearch && matchesCategory\n  })\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Public Files</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"p-6 border-b bg-gray-50 dark:bg-gray-700\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search files...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white\"\n              />\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-5 w-5 text-gray-400\" />\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white\"\n              >\n                <option value=\"all\">All Categories</option>\n                <option value=\"document\">Documents</option>\n                <option value=\"image\">Images</option>\n                <option value=\"video\">Videos</option>\n                <option value=\"audio\">Audio</option>\n                <option value=\"archive\">Archives</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* File List */}\n        <div className=\"flex-1 overflow-y-auto p-6\">\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n              <p className=\"mt-4 text-gray-600 dark:text-gray-400\">Loading public files...</p>\n            </div>\n          ) : filteredFiles.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <File className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 dark:text-gray-400\">No files found</p>\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {filteredFiles.map((file) => (\n                <div key={file.id} className=\"flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\">\n                  {getFileIcon(file.type, file.category)}\n                  <div className=\"flex-1 min-w-0\">\n                    <h3 className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {file.name}\n                    </h3>\n                    <div className=\"flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-400\">\n                      <span>{formatFileSize(file.size)}</span>\n                      <span>By: {file.uploaderId}</span>\n                      <span>{formatDate(file.uploadedAt)}</span>\n                      <span>{file.downloads} downloads</span>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => handleDownload(file)}\n                    className=\"flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\"\n                  >\n                    <Download className=\"h-4 w-4\" />\n                    <span>Download</span>\n                  </button>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAoBe,SAAS,YAAY,EAAE,OAAO,EAAoB;;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe;gBACjE;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK,IAAI,EAAE;YAC3B,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,SAAS,EAAE;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS,EAAE;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,MAAM,cAAc,CAAC,MAAc;QACjC,IAAI,aAAa,SAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAClD,IAAI,aAAa,SAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAClD,IAAI,aAAa,SAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAClD,IAAI,aAAa,WAAW,qBAAO,6LAAC,2MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACtD,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,oBAAoB;YACpB,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,KAAK,EAAE,EAAE,EAAE;gBAC7D,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe;gBACjE;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,uBAAuB;gBACvB,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,KAAK,IAAI;gBACtB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,wBAAwB;gBACxB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACxB,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,WAAW,EAAE,SAAS,GAAG;wBAAE,IAAI;YAE9D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC7E,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACxE,OAAO,iBAAiB;IAC1B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmD;;;;;;sCACjE,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAGd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOhC,6LAAC;oBAAI,WAAU;8BACZ,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;+BAErD,cAAc,MAAM,KAAK,kBAC3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;6CAGlD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAAkB,WAAU;;oCAC1B,YAAY,KAAK,IAAI,EAAE,KAAK,QAAQ;kDACrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,eAAe,KAAK,IAAI;;;;;;kEAC/B,6LAAC;;4DAAK;4DAAK,KAAK,UAAU;;;;;;;kEAC1B,6LAAC;kEAAM,WAAW,KAAK,UAAU;;;;;;kEACjC,6LAAC;;4DAAM,KAAK,SAAS;4DAAC;;;;;;;;;;;;;;;;;;;kDAG1B,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;;+BAlBA,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BjC;GApLwB;KAAA", "debugId": null}}, {"offset": {"line": 1461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/cloneit/vadukondi/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { Share2, Upload, Users, Search, Settings, LogOut } from 'lucide-react'\nimport FileUpload from '@/components/FileUpload'\nimport NetworkScan from '@/components/NetworkScan'\nimport PublicFiles from '@/components/PublicFiles'\n\nexport default function Dashboard() {\n  const [userId, setUserId] = useState<string | null>(null)\n  const [mounted, setMounted] = useState(false)\n  const [showFileUpload, setShowFileUpload] = useState(false)\n  const [showNetworkScan, setShowNetworkScan] = useState(false)\n  const [showPublicFiles, setShowPublicFiles] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n    // Check if user is authenticated\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('auth-token')\n      if (!token) {\n        window.location.href = '/'\n        return\n      }\n      \n      // TODO: Verify token and get user info\n      // For now, we'll extract userId from token or set a placeholder\n      setUserId('123456') // Placeholder\n    }\n  }, [])\n\n  const handleLogout = () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth-token')\n      window.location.href = '/'\n    }\n  }\n\n  if (!mounted) {\n    return null\n  }\n\n  if (!userId) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Share2 className=\"h-8 w-8 text-blue-600\" />\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Vadukondi</h1>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">ID: {userId}</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white\">\n                <Settings className=\"h-5 w-5\" />\n              </button>\n              <button \n                onClick={handleLogout}\n                className=\"p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white\"\n              >\n                <LogOut className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Quick Actions */}\n        <div className=\"grid md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <Upload className=\"h-8 w-8 text-blue-600\" />\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">Upload Files</h2>\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              Drag and drop files or click to upload. Share instantly with anyone.\n            </p>\n            <button\n              onClick={() => setShowFileUpload(true)}\n              className=\"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              Choose Files\n            </button>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <Users className=\"h-8 w-8 text-green-600\" />\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">Nearby Users</h2>\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              Discover and share files with users on your local network.\n            </p>\n            <button\n              onClick={() => setShowNetworkScan(true)}\n              className=\"w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              Scan Network\n            </button>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <Search className=\"h-8 w-8 text-purple-600\" />\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">Public Files</h2>\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              Browse and download files shared publicly by the community.\n            </p>\n            <button\n              onClick={() => setShowPublicFiles(true)}\n              className=\"w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              Browse Public\n            </button>\n          </div>\n        </div>\n\n        {/* File Management Sections */}\n        <div className=\"grid lg:grid-cols-2 gap-8\">\n          {/* My Files */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border\">\n            <div className=\"p-6 border-b\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">My Files</h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Files you've uploaded</p>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"text-center py-8\">\n                <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-600 dark:text-gray-400\">No files uploaded yet</p>\n                <p className=\"text-sm text-gray-500 dark:text-gray-500\">Upload your first file to get started</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Recent Activity */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border\">\n            <div className=\"p-6 border-b\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Recent Activity</h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Your sharing history</p>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"text-center py-8\">\n                <Share2 className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-600 dark:text-gray-400\">No activity yet</p>\n                <p className=\"text-sm text-gray-500 dark:text-gray-500\">Start sharing files to see your activity</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"container mx-auto px-4 py-6 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"text-center\">\n          <p className=\"text-sm text-gray-500 dark:text-gray-500\">\n            Created by <span className=\"font-semibold text-blue-600 dark:text-blue-400\">Shaik Mahammad Yaseen</span> •\n            Vadukondi v1.0\n          </p>\n        </div>\n      </footer>\n\n      {/* Modals */}\n      {mounted && showFileUpload && (\n        <FileUpload onClose={() => setShowFileUpload(false)} />\n      )}\n      {mounted && showNetworkScan && (\n        <NetworkScan onClose={() => setShowNetworkScan(false)} />\n      )}\n      {mounted && showPublicFiles && (\n        <PublicFiles onClose={() => setShowPublicFiles(false)} />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,WAAW;YACX,iCAAiC;YACjC,wCAAmC;gBACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,CAAC,OAAO;oBACV,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACvB;gBACF;gBAEA,uCAAuC;gBACvC,gEAAgE;gBAChE,UAAU,UAAU,cAAc;;YACpC;QACF;8BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;;oDAA2C;oDAAK;;;;;;;;;;;;;;;;;;;0CAGjE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;;;;;;;kDAEtE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;;;;;;;kDAEtE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;;;;;;;kDAEtE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAE1D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;0CAM9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAE1D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAA2C;0CAC3C,6LAAC;gCAAK,WAAU;0CAAiD;;;;;;4BAA4B;;;;;;;;;;;;;;;;;YAO7G,WAAW,gCACV,6LAAC,mIAAA,CAAA,UAAU;gBAAC,SAAS,IAAM,kBAAkB;;;;;;YAE9C,WAAW,iCACV,6LAAC,oIAAA,CAAA,UAAW;gBAAC,SAAS,IAAM,mBAAmB;;;;;;YAEhD,WAAW,iCACV,6LAAC,oIAAA,CAAA,UAAW;gBAAC,SAAS,IAAM,mBAAmB;;;;;;;;;;;;AAIvD;GAtLwB;KAAA", "debugId": null}}]}